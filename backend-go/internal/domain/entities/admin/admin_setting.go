package admin

import (
	"time"

	"gorm.io/gorm"
)

// AdminSetting menyimpan pengaturan global aplikasi yang dapat dikonfigurasi oleh admin.
// Diasumsikan hanya ada satu baris data di tabel ini.
type AdminSetting struct {
	ID        uint           `gorm:"primarykey" json:"-"`
	CreatedAt time.Time      `gorm:"type:timestamp without time zone" json:"-"`
	UpdatedAt time.Time      `gorm:"type:timestamp without time zone" json:"-"`
	DeletedAt gorm.DeletedAt `gorm:"index;type:timestamp without time zone" json:"-"`

	// Durasi dalam menit sebelum akun dianggap kadaluwarsa.
	ExpiredMinutes int `gorm:"not null" json:"expired_minutes"`

	// Waktu maintenance harian dalam format "HH:MM".
	DailyMtTime string `gorm:"type:varchar(5);not null" json:"daily_mt_time"`

	// Interval penagihan per jam dalam menit.
	HourlyBillingInterval int `gorm:"not null" json:"hourly_billing_interval"`

	// Saldo minimal yang harus dimiliki pengguna.
	MinSaldo int64 `gorm:"type:numeric(15,0);not null" json:"min_saldo"`

	// Nominal minimal untuk sekali top-up member.
	MinTopUp int64 `gorm:"type:numeric(15,0);not null;default:10000" json:"min_top_up"`

	// Nominal minimal untuk sekali top-up reseller.
	MinTopUpReseller int64 `gorm:"type:numeric(15,0);not null;default:50000" json:"min_top_up_reseller"`
}
