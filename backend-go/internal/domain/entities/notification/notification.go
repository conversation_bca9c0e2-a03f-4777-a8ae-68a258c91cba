package notification

import (
	"time"

	"gorm.io/gorm"
)

// NotificationType defines the type of notification
type NotificationType string

const (
	NotificationTypeTrial    NotificationType = "trial"
	NotificationTypeMonthly  NotificationType = "monthly"
	NotificationTypeHourly   NotificationType = "hourly"
	NotificationTypeRenewal  NotificationType = "renewal"
	NotificationTypeTopup    NotificationType = "topup"
	NotificationTypePayment  NotificationType = "payment"
	NotificationTypeBilling  NotificationType = "billing"
)

// Notification represents a user notification
type Notification struct {
	ID        uint             `gorm:"primarykey" json:"id"`
	CreatedAt time.Time        `gorm:"type:timestamp without time zone" json:"created_at"`
	UpdatedAt time.Time        `gorm:"type:timestamp without time zone" json:"updated_at"`
	DeletedAt gorm.DeletedAt   `gorm:"index;type:timestamp without time zone" json:"-"`
	UserID    uint             `gorm:"not null;index" json:"user_id"`
	Type      NotificationType `gorm:"type:varchar(50);not null;index" json:"type"`
	Title     string           `gorm:"type:varchar(255);not null" json:"title"`
	Message   string           `gorm:"type:text;not null" json:"message"`
	Data      *string          `gorm:"type:jsonb" json:"data,omitempty"` // JSON data for additional info
	IsRead    bool             `gorm:"default:false;index" json:"is_read"`
	ReadAt    *time.Time       `gorm:"type:timestamp without time zone" json:"read_at,omitempty"`
}

// NotificationData represents additional data that can be stored with notification
type NotificationData struct {
	AccountID    *uint   `json:"account_id,omitempty"`
	AccountType  *string `json:"account_type,omitempty"`
	Username     *string `json:"username,omitempty"`
	ServerCode   *string `json:"server_code,omitempty"`
	Amount       *int64  `json:"amount,omitempty"`
	InvoiceID    *string `json:"invoice_id,omitempty"`
	Duration     *string `json:"duration,omitempty"`
	PaymentMethod *string `json:"payment_method,omitempty"`
}

// NotificationResponse represents the API response for notifications
type NotificationResponse struct {
	ID        uint             `json:"id"`
	Type      NotificationType `json:"type"`
	Title     string           `json:"title"`
	Message   string           `json:"message"`
	Data      *NotificationData `json:"data,omitempty"`
	IsRead    bool             `json:"is_read"`
	CreatedAt time.Time        `json:"created_at"`
	ReadAt    *time.Time       `json:"read_at,omitempty"`
}

// ToResponse converts Notification to NotificationResponse
func (n *Notification) ToResponse() NotificationResponse {
	response := NotificationResponse{
		ID:        n.ID,
		Type:      n.Type,
		Title:     n.Title,
		Message:   n.Message,
		IsRead:    n.IsRead,
		CreatedAt: n.CreatedAt,
		ReadAt:    n.ReadAt,
	}

	// Parse JSON data if exists
	if n.Data != nil {
		// You can add JSON parsing here if needed
		// For now, we'll keep it simple
	}

	return response
}

// NotificationListResponse represents paginated notification list
type NotificationListResponse struct {
	Notifications []NotificationResponse `json:"notifications"`
	Total         int64                  `json:"total"`
	Page          int                    `json:"page"`
	Limit         int                    `json:"limit"`
	UnreadCount   int64                  `json:"unread_count"`
}

// MarkAsReadRequest represents request to mark notifications as read
type MarkAsReadRequest struct {
	NotificationIDs []uint `json:"notification_ids"`
}

// NotificationStatsResponse represents notification statistics
type NotificationStatsResponse struct {
	UnreadCount int64 `json:"unread_count"`
	TotalCount  int64 `json:"total_count"`
}
