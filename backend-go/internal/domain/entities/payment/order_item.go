package payment

import (
	"time"

	"gorm.io/gorm"
)

// OrderItem represents a single item in a transaction, especially for payment gateways.
// @Description OrderItem contains details for an item being purchased.
// @Swagger:model OrderItem
type OrderItem struct {
	ID            uint           `gorm:"primaryKey" json:"id"`
	TransactionID uint           `gorm:"index;not null" json:"transaction_id"` // Foreign key to Transaction
	SKU           string         `gorm:"not null" json:"sku"`
	Name          string         `gorm:"not null" json:"name"`
	Price         int64          `gorm:"not null" json:"price"`
	Quantity      int            `gorm:"not null" json:"quantity"`
	MerchantRef   string         `gorm:"not null;index" json:"merchant_ref"`
	CreatedAt     time.Time      `gorm:"type:timestamp without time zone" json:"created_at"`
	UpdatedAt     time.Time      `gorm:"type:timestamp without time zone" json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName specifies the table name for the OrderItem model.
func (OrderItem) TableName() string {
	return "order_items"
}
