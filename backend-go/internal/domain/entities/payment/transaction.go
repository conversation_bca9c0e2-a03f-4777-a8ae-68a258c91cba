package payment

import (
	"time"
	"vpn-shop/backend-go/internal/domain/entities/user"

	"gorm.io/gorm"
)

// TransactionType mendefinisikan tipe transaksi
type TransactionType string

const (
	Topup           TransactionType = "TOPUP"
	PurchaseMonthly TransactionType = "PURCHASE_MONTHLY"
	PurchaseHourly  TransactionType = "PURCHASE_HOURLY"
	Trial           TransactionType = "TRIAL"
	Renewal         TransactionType = "RENEWAL"
	Refund          TransactionType = "REFUND"
	Billing         TransactionType = "BILLING"
	BilledHourly    TransactionType = "BILLED_HOURLY"
)

// TransactionStatus mendefinisikan status transaksi
type TransactionStatus string

const (
	Pending TransactionStatus = "PENDING"
	Success TransactionStatus = "SUCCESS"
	Failed  TransactionStatus = "FAILED"
	Expired TransactionStatus = "EXPIRED"
)

// Transaction adalah model untuk mencatat semua transaksi keuangan
type Transaction struct {
	ID                 uint              `gorm:"primaryKey" json:"id"`
	InvoiceID          string            `gorm:"uniqueIndex;not null" json:"invoice_id"`
	UserID             uint              `gorm:"index;not null" json:"user_id"`
	User               user.User         `json:"user"`
	AccountID          *uint             `gorm:"index" json:"account_id,omitempty"`
	AccountType        *string           `gorm:"index;type:varchar(20)" json:"account_type,omitempty"`
	Duration           *string           `json:"duration,omitempty"`
	Type               TransactionType   `gorm:"type:varchar(20);not null" json:"type"`
	Description        string            `json:"description"`
	Amount             int64             `gorm:"not null" json:"amount"`
	Status             TransactionStatus `gorm:"type:varchar(20);not null;index" json:"status"`
	PaymentGateway     *string           `json:"payment_gateway"`
	GatewayReference   *string           `gorm:"index" json:"gateway_reference"`
	GatewayCheckoutURL *string           `json:"gateway_checkout_url"`
	CreatedAt          time.Time         `gorm:"type:timestamp without time zone" json:"created_at"`
	UpdatedAt          time.Time         `gorm:"type:timestamp without time zone" json:"updated_at"`
	DeletedAt          gorm.DeletedAt    `gorm:"index" json:"-"`
}

// TableName menentukan nama tabel
func (Transaction) TableName() string {
	return "transactions"
}
