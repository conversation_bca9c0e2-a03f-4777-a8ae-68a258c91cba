package shared

import "time"

// ErrorResponse defines the standard structure for API error responses.
type ErrorResponse struct {
	Error   string      `json:"error"`
	Details interface{} `json:"details,omitempty"` // Menggunakan interface{} agar bisa menampung berbagai jenis detail error
}

// SuccessResponse defines the standard structure for successful API responses.
type SuccessResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
}

// MarzbanUserResponse defines the structure for the user data received from Marzban API.
type MarzbanUserResponse struct {
	Username        string   `json:"username"`
	Status          string   `json:"status"`
	UsedTraffic     int64    `json:"used_traffic"`
	DataLimit       int64    `json:"data_limit"`
	Links           []string `json:"links"`
	SubscriptionURL string   `json:"subscription_url"`
	Protocol        string   `json:"protocol"`
}

// ProcessedLink represents a connection link with a user-friendly name.
type ProcessedLink struct {
	Name string `json:"name"`
	URL  string `json:"url"`
}

// ServerDetailResponse defines the publicly exposed server fields for the account detail endpoint.
type ServerDetailResponse struct {
	ServerID      uint      `json:"server_id"`
	Nama          string    `json:"nama"`
	Kode          string    `json:"kode"`
	Domain        string    `json:"domain"`
	Negara        string    `json:"negara"`
	NamaISP       string    `json:"nama_isp"`
	HargaMember   int       `json:"harga_member"`
	HargaReseller int       `json:"harga_reseller"`
	SSH           string    `json:"ssh"`
	Trojan        string    `json:"trojan"`
	Vmess         string    `json:"vmess"`
	Vless         string    `json:"vless"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// ServerInfoForAccountDetail defines the public-facing server data for an account detail response.
type ServerInfoForAccountDetail struct {
	ServerID      uint      `json:"server_id"`
	Nama          string    `json:"nama"`
	Kode          string    `json:"kode"`
	Domain        string    `json:"domain"`
	Negara        string    `json:"negara"`
	NamaIsp       string    `json:"nama_isp"`
	HargaMember   int64     `json:"harga_member"`
	HargaReseller int64     `json:"harga_reseller"`
	SSH           string    `json:"ssh"`
	Trojan        string    `json:"trojan"`
	Vmess         string    `json:"vmess"`
	Vless         string    `json:"vless"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// AccountDetailResponse defines the structure for the account detail API response.
type AccountDetailResponse struct {
	Username        string                     `json:"username"`
	UUID            string                     `json:"uuid"`
	AccountType     string                     `json:"account_type"`
	Status          string                     `json:"status"`
	Protocol        string                     `json:"protocol"`
	ExpiredDate     time.Time                  `json:"expired_date"`
	DataLimitGB     float64                    `json:"data_limit_gb"`
	UsedTrafficGB   float64                    `json:"used_traffic_gb"`
	SubscriptionURL string                     `json:"subscription_url"`
	ConnectionLinks []ProcessedLink            `json:"connection_links"`
	Server          ServerInfoForAccountDetail `json:"server"`
}

// RenewSuccessResponse defines the structure for a successful account renewal.
type RenewSuccessResponse struct {
	Message    string `json:"message"    example:"Account renewed successfully"`
	NewBalance int64  `json:"new_balance"  example:"50000"`
	NewExpire  string `json:"new_expire"   example:"2024-12-31"`
}

// TopUpResponse defines the response for a successful top-up initiation.
type TopUpResponse struct {
	CheckoutURL string `json:"checkout_url" example:"https://tripay.co.id/checkout/INV12345"`
}

// CheckoutResponse defines the response for any action that results in a checkout URL.
type CheckoutResponse struct {
	CheckoutURL string `json:"checkout_url"`
	InvoiceID   string `json:"invoice_id,omitempty"`
}

// Pagination defines the standard structure for paginated API responses.
type Pagination struct {
	Total       int64 `json:"total"        example:"100"`
	PerPage     int64 `json:"per_page"     example:"10"`
	CurrentPage int64 `json:"current_page" example:"1"`
	LastPage    int64 `json:"last_page"    example:"10"`
}
