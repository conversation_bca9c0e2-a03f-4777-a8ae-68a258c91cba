package user

import "time"

// ServiceHistoryItem represents a unified view of a purchased service for the history page.
// It contains only the necessary fields for the history table and invoice view.
type ServiceHistoryItem struct {
	// Fields for table display
	TanggalBeli time.Time `json:"tanggal_beli"`
	Username    string    `json:"username"`              // The account username (e.g., trial-xyz)
	Layanan     string    `json:"layanan"`               // The server domain
	NamaServer  string    `json:"nama_server,omitempty"` // The server name
	NamaIsp     string    `json:"nama_isp,omitempty"`    // The ISP name
	Status      string    `json:"status"`
	Tipe        string    `json:"tipe"`         // e.g., "trojan-monthly"
	ServiceType string    `json:"service_type"` // "trojan", "vmess", "vless", "ssh"
	OrderID     string    `json:"order_id"`    // Unique identifier for the order/account
	KodeServer  string    `json:"kode_server"`  // Server code for navigation to detail page
	
	// Fields for invoice page (when clicking "Detail")
	Expired    *time.Time `json:"expired,omitempty"`
	KodeAkun   string     `json:"kode_akun"`
}

// ServiceHistoryResponse wraps the list of history items.
type ServiceHistoryResponse struct {
	History []ServiceHistoryItem `json:"history"`
	Page    int                  `json:"page"`
	Limit   int                  `json:"limit"`
	Total   int64                `json:"total"`
}
