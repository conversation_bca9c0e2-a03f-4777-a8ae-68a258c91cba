package user

import (
	"time"

	"gorm.io/gorm"
)

type Role struct {
	ID          uint           `gorm:"primarykey"`
	CreatedAt   time.Time      `gorm:"type:timestamp without time zone"`
	UpdatedAt   time.Time      `gorm:"type:timestamp without time zone"`
	DeletedAt   gorm.DeletedAt `gorm:"index;type:timestamp without time zone" swaggertype:"string" format:"date-time"`
	Name        string         `gorm:"type:varchar(255);uniqueIndex;not null"`
	Description *string
	Users       []*User `gorm:"many2many:user_roles;"`
}
