# VPN Shop Frontend

Frontend aplikasi VPN Shop yang dibangun dengan Next.js, TypeScript, Tailwind CSS, dan shadcn/ui.

## 🚀 Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Authentication**: NextAuth.js
- **Form Handling**: React Hook Form + Zod
- **Icons**: Lucide React

## 📁 Struktur Proyek

```
src/
├── app/                    # App Router pages
│   ├── auth/
│   │   └── signin/        # Halaman login
│   ├── dashboard/         # Halaman dashboard
│   ├── api/auth/          # NextAuth API routes
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   ├── page.tsx          # Home page (redirect logic)
│   └── not-found.tsx     # Custom 404 page
├── components/
│   ├── ui/               # shadcn/ui components
│   └── providers.tsx     # App providers
├── contexts/             # React contexts
├── hooks/               # Custom hooks
├── lib/                 # Utilities
├── types/               # TypeScript types
└── ...
```

## 🛠️ Setup & Installation

1. **Clone dan masuk ke direktori**:
   ```bash
   cd frontend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Setup environment variables**:
   ```bash
   cp .env.local.example .env.local
   ```
   
   Edit `.env.local` dan sesuaikan dengan konfigurasi Anda:
   ```env
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your-secret-key-here
   NEXT_PUBLIC_API_URL=http://localhost:8080
   ```

4. **Jalankan development server**:
   ```bash
   npm run dev
   ```

5. **Buka browser**: [http://localhost:3000](http://localhost:3000)

## 🔐 Authentication

Aplikasi menggunakan NextAuth.js dengan Credentials Provider yang terintegrasi dengan backend API.

### Flow Authentication:
1. User mengakses halaman login (`/auth/signin`)
2. Form login mengirim credentials ke NextAuth
3. NextAuth memvalidasi dengan backend API (`/auth/login`)
4. Jika berhasil, user diarahkan ke dashboard
5. Session disimpan dalam JWT

## 🎨 UI Components

Menggunakan shadcn/ui untuk komponen UI yang konsisten:
- Button, Input, Label
- Card, Form
- Custom Notification system

## 📱 Pages

- **`/`** - Home (redirect logic)
- **`/auth/signin`** - Halaman login
- **`/dashboard`** - Dashboard utama
- **`/404`** - Custom 404 page

## 🔧 Development

### Menambah shadcn/ui Components:
```bash
npx shadcn@latest add [component-name]
```

### Build untuk Production:
```bash
npm run build
npm start
```

### Linting:
```bash
npm run lint
```

## 🤝 Contributing

1. Ikuti struktur folder yang ada
2. Gunakan TypeScript untuk type safety
3. Ikuti konvensi penamaan yang konsisten
4. Tambahkan dokumentasi untuk komponen baru

## 📝 Notes

- Proyek ini menggunakan clean architecture
- Semua komponen menggunakan "use client" directive jika diperlukan
- Environment variables harus diset sebelum menjalankan aplikasi
- Backend API harus berjalan di port yang sesuai dengan `NEXT_PUBLIC_API_URL`
