import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Home, ArrowLeft, Search } from "lucide-react";

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-red-100 mb-4">
              <Search className="h-12 w-12 text-red-600" />
            </div>
            <CardTitle className="text-3xl font-bold text-gray-900">
              404
            </CardTitle>
            <CardDescription className="text-lg">
              Halaman Tidak Ditemukan
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-6">
            <p className="text-gray-600">
              <PERSON><PERSON>, halaman yang Anda cari tidak dapat ditemukan. 
              Mungkin halaman tersebut telah dipindahkan atau tidak pernah ada.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button asChild>
                <Link href="/" className="flex items-center space-x-2">
                  <Home className="h-4 w-4" />
                  <span>Kembali ke Beranda</span>
                </Link>
              </Button>
              
              <Button variant="outline" asChild>
                <Link href="/dashboard" className="flex items-center space-x-2">
                  <ArrowLeft className="h-4 w-4" />
                  <span>Ke Dashboard</span>
                </Link>
              </Button>
            </div>

            <div className="pt-4 border-t">
              <p className="text-sm text-gray-500">
                Jika Anda yakin ini adalah kesalahan, silakan{" "}
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-blue-600 hover:text-blue-500 underline"
                >
                  hubungi dukungan
                </a>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
