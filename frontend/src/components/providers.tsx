"use client";

import { SessionProvider } from "next-auth/react";
import { ThemeProvider } from "@/components/theme-provider";
import { NotificationProvider } from "@/contexts/NotificationContext";
import { NotificationContainer } from "@/components/ui/notification";

interface ProvidersProps {
  children: React.ReactNode;
}

export function Providers({ children }: ProvidersProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
      storageKey="vpn-shop-theme"
    >
      <SessionProvider>
        <NotificationProvider>
          {children}
          <NotificationContainer />
        </NotificationProvider>
      </SessionProvider>
    </ThemeProvider>
  );
}
