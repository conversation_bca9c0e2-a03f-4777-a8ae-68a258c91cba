# VPN Shop - Frontend

This is the frontend for the VPN Shop application, built with Next.js, React, and TypeScript. It provides the user interface for interacting with the VPN Shop services, including user authentication, profile management, and service browsing.

Template next admin https://demo.nextadmin.co https://github.com/NextAdminHQ/nextjs-admin-dashboard

## Tech Stack

-   **Framework**: [Next.js](https://nextjs.org/) 13+ (App Router)
-   **Language**: [TypeScript](https://www.typescriptlang.org/)
-   **UI Library**: [React](https://reactjs.org/)
-   **Styling**: [Tailwind CSS](https://tailwindcss.com/)
-   **Authentication**: [NextAuth.js](https://next-auth.js.org/)
-   **Backend**: Go (communicated via REST API)

## Getting Started

Follow these instructions to get a copy of the project up and running on your local machine for development and testing purposes.

### Prerequisites

-   [Node.js](https://nodejs.org/) (v18.x or later recommended)
-   [npm](https://www.npmjs.com/) or [yarn](https://yarnpkg.com/)

### Installation

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    cd frondend
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    # or
    yarn install
    ```

3.  **Set up environment variables:**
    Create a file named `.env.local` in the root of the `frondend` directory and add the following variables. These are necessary for the application to connect to the backend and for authentication to work correctly.

    ```env
    # URL of the Go backend API
    NEXT_PUBLIC_API_URL=http://localhost:8080/api/v1

    # A secret key for NextAuth.js to sign and encrypt session cookies.
    # You can generate a secret using: openssl rand -base64 32
    NEXTAUTH_SECRET=your-super-secret-key

    # (Optional) Demo user credentials for quick login
    NEXT_PUBLIC_DEMO_USER_MAIL=<EMAIL>
    NEXT_PUBLIC_DEMO_USER_PASS=password123
    ```

4.  **Run the development server:**
    ```bash
    npm run dev
    # or
    yarn dev
    ```
    Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Core Concepts

### Routing
The project uses the **Next.js App Router**. Routing is file-system based.
-   To create a new page (route), you create a new folder inside `src/app/`.
-   The folder name corresponds to the URL path. For example, `src/app/dashboard` creates the `/dashboard` route.
-   Inside each route folder, you must create a `page.tsx` file, which is the main component for that page.

### Authentication Flow
This project uses **NextAuth.js** to handle user authentication. The flow is designed to be secure and robust, keeping sensitive tokens out of the browser's `localStorage`.

1.  **Login Request**: When a user submits their credentials on the sign-in page, the frontend calls the `signIn()` function from NextAuth.js.
2.  **Backend Communication**: This triggers the `authorize` function within the `CredentialsProvider` located at `src/app/api/auth/[...nextauth]/route.ts`. This function is the only part of the frontend that communicates directly with the backend's `/auth/login` endpoint to verify credentials.
3.  **Token Handling**: If the backend login is successful, it returns a JWT. NextAuth.js takes this token and creates a secure, HTTP-only session cookie. The JWT from the backend is stored within this session, not in `localStorage`.
4.  **Authenticated API Calls**: For any subsequent API calls to protected backend routes (e.g., fetching user profile), the frontend uses the `useSession()` hook from NextAuth.js to access the session data, retrieve the `accessToken`, and include it in the `Authorization: Bearer <token>` header.

This approach enhances security by preventing Cross-Site Scripting (XSS) attacks from accessing the JWT.

### Styling
The project primarily uses **Tailwind CSS** for utility-first styling.
-   Global stylesheets and custom CSS are located in the `src/css/` directory.
-   **Adding New CSS Files**: To add a new global stylesheet, follow these steps:
    1.  Create your new `.css` file inside the `src/css/` directory (e.g., `src/css/custom-styles.css`).
    2.  Import it into the root layout file at `src/app/layout.tsx` alongside the other CSS imports.
        ```tsx
        // src/app/layout.tsx
        import "@/css/style.css";
        import "@/css/form.css";
        import "@/css/notification.css";
        import "@/css/custom-styles.css"; // Add your new file here
        ```

### Notification System
The application features a global notification system to provide feedback to the user (e.g., success or error messages).
-   It is built using React's Context API. The provider is located in `src/contexts/NotificationContext.tsx` and is wrapped around the application in `src/app/layout.tsx`.
-   **How to Use**: To trigger a notification from any client component (`"use client";`), import and use the `useNotification` hook.

    ```tsx
    import { useNotification } from '@/contexts/NotificationContext';

    function MyComponent() {
      const { addNotification } = useNotification();

      const handleClick = () => {
        // Show a success notification
        addNotification('Profile updated successfully!', 'success');

        // Show an error notification
        addNotification('Failed to update profile.', 'error');
      };

      return <button onClick={handleClick}>Update Profile</button>;
    }
    ```

## Project Structure

-   `src/app/`: Contains all pages, layouts, and routes.
    -   `src/app/api/`: Handles API routes, including the NextAuth.js logic.
    -   `src/app/profile/`: An example of a protected route that requires authentication.
-   `src/components/`: Shared React components (e.g., `InputGroup`, `Sidebar`, `Header`).
-   `src/contexts/`: React Context providers, like `NotificationContext`.
-   `src/lib/`: Utility functions or library configurations.
-   `src/css/`: Global stylesheets and Tailwind CSS configuration.
-   `public/`: Static assets like images and fonts.

## Available Scripts

In the project directory, you can run:

-   `npm run dev`: Runs the app in development mode.
-   `npm run build`: Builds the app for production.
-   `npm run start`: Starts a production server.
-   `npm run lint`: Runs the linter to check for code quality issues.

---

## 📁 Project Structure (Current)

### 🎯 Main Pages (Production)
```
src/app/(main)/
├── dashboard/             # ✅ Main dashboard
├── product/               # ✅ VPN products (trojan, vless, vmess)
├── detail/                # ✅ Account details & management
├── top-up/                # ✅ Top-up saldo
├── transactions/          # ✅ Transaction history
├── history/               # ✅ Service history
├── profile/               # ✅ User profile
├── notifications/         # ✅ User notifications
└── admin/                 # ✅ Admin panel (uses Charts)
```

### 🧩 Main Components (Production)
```
src/components/
├── Auth/                  # ✅ Authentication components
├── Dashboard/             # ✅ Dashboard-specific components
├── Tables/                # ✅ Custom table components
├── TopUp/                 # ✅ Top-up & payment components
├── product/               # ✅ Product-related components
├── Layouts/               # ✅ Layout (header, sidebar, footer)
├── Charts/                # ✅ Charts (used in admin dashboard)
└── ui/                    # ✅ Core UI components
```

### 📋 Demo Pages (Ignore/Template)
```
src/app/(main)/
├── charts/                # 🔸 Demo (but Charts component is used)
├── forms/                 # ❌ Demo forms
├── ui-elements/           # ❌ Demo UI elements
├── tables/                # ❌ Demo tables (not our Tables/)
└── pages/                 # ❌ Demo pages
```

### 💡 Navigation Tips
- **Main functionality**: Use sidebar navigation
- **Demo pages**: Can be ignored (will be cleaned up later)
- **Charts component**: Used in admin dashboard (keep it)
- **Focus areas**: dashboard, product, profile, admin

### 🔄 Future Refactoring
- Structure cleanup planned after all features are complete
- Demo files removal when stable
- Folder reorganization for better navigation
- This approach prioritizes functionality over perfect structure initially
