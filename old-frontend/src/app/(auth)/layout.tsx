"use client";

import { ThemeToggleSwitch } from "@/components/Layouts/header/theme-toggle";
import type { PropsWithChildren } from "react";

export default function AuthLayout({ children }: PropsWithChildren) {
  return (
    <>
      <div className="fixed right-6 top-6 z-50">
        <ThemeToggleSwitch />
      </div>
      <main className="flex min-h-screen items-center justify-center bg-gray-2 dark:bg-[#020d1a]">
        {children}
      </main>
    </>
  );
}
