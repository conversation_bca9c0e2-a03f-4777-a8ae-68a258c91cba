import { Suspense } from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Manajemen Server - Admin',
  description: 'Halaman untuk mengelola data server VPS.',
};
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { createAuthenticatedApiClient } from '@/lib/apiClientEnhanced';
import ServerTable from '@/components/Tables/ServerTable';
import Breadcrumb from '@/components/Breadcrumbs/Breadcrumb';

async function getServers() {
  const session = await getServerSession(authOptions);
  const accessToken = (session as any)?.accessToken;

  if (!accessToken) {
    // Middleware should handle the redirect, but we can throw an error
    // as a fallback for unexpected cases.
    throw new Error("Tidak terautentikasi atau token tidak ada.");
  }

  try {
    const apiClient = await createAuthenticatedApiClient();
    const data = await apiClient.get('/servers');
    // Ekstrak array servers dari respons API
    return data.servers || [];
  } catch (error) {
    console.error('Gagal mengambil data server:', error);
    throw new Error("Gagal mengambil data server.");
  }
}

const ServerManagementPage = async () => {
  const servers = await getServers();

  return (
    <>
      <Breadcrumb pageName="Manajemen Server" />
      <div className="flex flex-col gap-10">
        <Suspense fallback={<div>Memuat server...</div>}>
          <ServerTable servers={servers} />
        </Suspense>
      </div>
    </>
  );
};

export default ServerManagementPage;