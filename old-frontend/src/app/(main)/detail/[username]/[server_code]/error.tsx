'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui-elements/button';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Account detail page error:', error);
  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] text-center px-4">
      <h2 className="text-2xl font-semibold mb-2"><PERSON><PERSON><PERSON><PERSON></h2>
      <p className="text-gray-500 dark:text-gray-400 mb-8 max-w-md">
        Maaf, terjadi kesalahan saat memuat detail akun. Silakan coba lagi nanti.
      </p>
      <div className="flex flex-col sm:flex-row gap-4">
        <Button label="Coba Lagi" variant="outlinePrimary" onClick={reset} />
        <Link href="/history">
          <Button label="Kembali ke Riwayat" variant="primary" />
        </Link>
      </div>
    </div>
  );
}