import { Skeleton } from '@/components/ui/skeleton';
import Breadcrumb from '@/components/Breadcrumbs/Breadcrumb';
import { ShowcaseSection } from '@/components/Layouts/showcase-section';

export default function Loading() {
  return (
    <>
      <Breadcrumb pageName="Detail Akun" />

      <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
        <ShowcaseSection title="Informasi Akun" className="!p-7">
          <div className="space-y-6">
            <div className="flex flex-col space-y-1">
              <span className="text-sm text-gray-500 dark:text-gray-400">Username</span>
              <Skeleton className="h-7 w-40" />
            </div>

            <div className="flex flex-col space-y-1">
              <span className="text-sm text-gray-500 dark:text-gray-400">Status</span>
              <Skeleton className="h-7 w-24" />
            </div>

            <div className="flex flex-col space-y-1">
              <span className="text-sm text-gray-500 dark:text-gray-400">Protokol</span>
              <Skeleton className="h-7 w-24" />
            </div>

            <div className="flex flex-col space-y-1">
              <span className="text-sm text-gray-500 dark:text-gray-400">Tanggal Kedaluwarsa</span>
              <Skeleton className="h-7 w-40" />
            </div>

            <div className="flex flex-col space-y-1">
              <span className="text-sm text-gray-500 dark:text-gray-400">Server</span>
              <Skeleton className="h-7 w-60" />
            </div>

            <div className="flex flex-col space-y-1">
              <span className="text-sm text-gray-500 dark:text-gray-400">Penggunaan Data</span>
              <Skeleton className="h-7 w-48" />
              <Skeleton className="h-2.5 w-full" />
            </div>

            <div className="flex flex-col space-y-1">
              <span className="text-sm text-gray-500 dark:text-gray-400">URL Langganan</span>
              <Skeleton className="h-7 w-full" />
            </div>

            <div className="pt-4">
              <Skeleton className="h-10 w-full" />
            </div>
          </div>
        </ShowcaseSection>

        <ShowcaseSection title="Link Koneksi" className="!p-7">
          <div className="space-y-6">
            {[1, 2, 3, 4].map((index) => (
              <div key={index} className="flex flex-col space-y-1">
                <span className="text-sm text-gray-500 dark:text-gray-400">Link {index}</span>
                <Skeleton className="h-7 w-full" />
              </div>
            ))}
          </div>
        </ShowcaseSection>
      </div>
    </>
  );
}