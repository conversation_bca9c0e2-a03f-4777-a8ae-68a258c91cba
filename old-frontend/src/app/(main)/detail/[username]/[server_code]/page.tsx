import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createAuthenticatedApiClient } from '@/lib/apiClientEnhanced';
import AccountDetailClient from './AccountDetailClient';

export const dynamic = 'force-dynamic';

interface AccountDetailPageProps {
  params: Promise<{ username: string; server_code: string }>;
}

interface AccountDetail {
  username: string;
  account_type: string;
  status: string;
  protocol: string;
  expired_date: string;
  data_limit_gb: number;
  used_traffic_gb: number;
  subscription_url: string;
  connection_links: Array<{ name: string; url: string }>;
  server: {
    server_id: number;
    nama: string;
    kode: string;
    domain: string;
    negara: string;
    nama_isp: string;
    harga_member: number;
    harga_reseller: number;
    ssh: string;
    trojan: string;
    vmess: string;
    vless: string;
    created_at: string;
    updated_at: string;
  };
}

async function fetchAccountDetail(
  username: string,
  serverCode: string,
  token: string | undefined
): Promise<{ accountDetail: AccountDetail | null; error?: string }> {
  if (!token) {
    return { accountDetail: null, error: 'Sesi otentikasi tidak ditemukan.' };
  }

  try {
    const apiClient = await createAuthenticatedApiClient();
    const data = await apiClient.get(`/accounts/detail/${username}/${serverCode}`);
    return { accountDetail: data };
  } catch (err: any) {
    console.error("Error fetching account detail:", err);
    return { accountDetail: null, error: err.message || 'Terjadi kesalahan pada server.' };
  }
}

export async function generateMetadata({ params }: AccountDetailPageProps): Promise<Metadata> {
  const { username, server_code } = await params;
  const session = await getServerSession(authOptions);
  const { accountDetail } = await fetchAccountDetail(username, server_code, session?.accessToken);

  if (!accountDetail) {
    return {
      title: 'Akun Tidak Ditemukan',
    };
  }

  return {
    title: `Detail Akun: ${accountDetail.username}`,
    description: `Detail akun ${accountDetail.protocol} untuk ${accountDetail.username} di server ${accountDetail.server.nama}.`,
  };
}

const AccountDetailPage = async ({ params }: AccountDetailPageProps) => {
  const { username, server_code } = await params;
  const session = await getServerSession(authOptions);
  const { accountDetail, error } = await fetchAccountDetail(username, server_code, session?.accessToken);

  if (!accountDetail) {
    notFound();
  }

  return <AccountDetailClient accountDetail={accountDetail} error={error} />;
};

export default AccountDetailPage;