import { Metadata } from 'next';
import Breadcrumb from '@/components/Breadcrumbs/Breadcrumb';
import HistoryTable from "@/components/Tables/HistoryTable";
import { createAuthenticatedApiClient } from '@/lib/apiClientEnhanced';
import { HistoryApiResponse } from "@/types/history";

export const metadata: Metadata = {
  title: `Riwayat Layanan | ${process.env.NEXT_PUBLIC_APP_NAME}`,
  description: `Halaman riwayat layanan untuk melihat semua layanan aktif dan riwayat pembelian Anda.`,
};

interface HistoryPageProps {
  searchParams?: Promise<{
    page?: string | string[];
    limit?: string | string[];
    search?: string | string[];
  }>;
}

const fetchHistoryData = async (searchParams: {
    page?: string | string[];
    limit?: string | string[];
    search?: string | string[];
  } | undefined): Promise<HistoryApiResponse> => {
    const apiClient = await createAuthenticatedApiClient();
    
    if (!apiClient) {
        return { history: [], page: 1, limit: 10, total: 0, message: "Authentication required" };
    }

    // Await searchParams sebelum mengakses propertinya
    const resolvedParams = await searchParams;
    const pageRaw = resolvedParams?.page;
    const limitRaw = resolvedParams?.limit;
    const searchRaw = resolvedParams?.search;

    const pageValue = Array.isArray(pageRaw) ? pageRaw[0] : pageRaw;
    const limitValue = Array.isArray(limitRaw) ? limitRaw[0] : limitRaw;
    const searchValue = Array.isArray(searchRaw) ? searchRaw[0] : searchRaw;

    const page = typeof pageValue === 'string' ? Number(pageValue) : 1;
    const limit = typeof limitValue === 'string' ? Number(limitValue) : 10;
    const search = typeof searchValue === 'string' ? searchValue : '';

    const params = new URLSearchParams({
        page: String(page),
        limit: String(limit),
        search,
    });

    try {
        const data = await apiClient.get(`/accounts/history?${params.toString()}`);
        return data;
    } catch (error) {
        console.error("Error fetching history data:", error);
        return { history: [], page: Number(page), limit: Number(limit), total: 0, message: "An error occurred" };
    }
};

const HistoryPage = async ({ searchParams }: HistoryPageProps) => {
  const resolvedSearchParams = await searchParams;
  const initialHistoryData = await fetchHistoryData(resolvedSearchParams);

  return (
    <>
      <Breadcrumb pageName="Riwayat Layanan" />

      <div className="flex flex-col gap-10">
        <HistoryTable initialData={initialHistoryData} />
      </div>
    </>
  );
};

export default HistoryPage;
