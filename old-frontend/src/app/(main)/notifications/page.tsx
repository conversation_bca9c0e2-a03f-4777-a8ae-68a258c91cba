"use client";

import { useState, useEffect } from 'react';
import { useNotifications } from '@/hooks/useNotifications';
import { formatDistanceToNow } from 'date-fns';
import { id } from 'date-fns/locale';
import { cn } from '@/lib/utils';

// Notification type icons
const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'trial':
      return '🆓';
    case 'monthly':
      return '📅';
    case 'hourly':
      return '⏰';
    case 'renewal':
      return '🔄';
    case 'topup':
      return '💰';
    case 'payment':
      return '💳';
    case 'billing':
      return '📊';
    default:
      return '🔔';
  }
};

const getNotificationTypeLabel = (type: string) => {
  switch (type) {
    case 'trial':
      return 'Trial';
    case 'monthly':
      return 'Pembelian Bulanan';
    case 'hourly':
      return 'Pembelian Hourly';
    case 'renewal':
      return 'Perpanjangan';
    case 'topup':
      return 'Top Up';
    case 'payment':
      return 'Pembayaran';
    case 'billing':
      return 'Billing';
    default:
      return 'Notifikasi';
  }
};

export default function NotificationsPage() {
  const {
    notifications,
    stats,
    loading,
    error,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    hasUnread,
    unreadCount
  } = useNotifications();

  const [currentPage, setCurrentPage] = useState(1);
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [loadingMore, setLoadingMore] = useState(false);

  // Initial fetch
  useEffect(() => {
    fetchNotifications(1, 20);
  }, [fetchNotifications]);

  // Load more notifications
  const loadMore = async () => {
    if (loadingMore) return;
    
    setLoadingMore(true);
    try {
      await fetchNotifications(currentPage + 1, 20);
      setCurrentPage(prev => prev + 1);
    } catch (error) {
      console.error('Failed to load more notifications:', error);
    } finally {
      setLoadingMore(false);
    }
  };

  // Filter notifications
  const filteredNotifications = notifications?.filter(notification => {
    if (filter === 'unread' && notification.is_read) return false;
    if (filter === 'read' && !notification.is_read) return false;
    if (typeFilter !== 'all' && notification.type !== typeFilter) return false;
    return true;
  }) || [];

  // Handle notification click
  const handleNotificationClick = async (notification: any) => {
    if (!notification.is_read) {
      try {
        await markAsRead([notification.id]);
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    }
  };

  // Handle mark all as read
  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  // Handle delete notification
  const handleDeleteNotification = async (notificationId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await deleteNotification(notificationId);
    } catch (error) {
      console.error('Failed to delete notification:', error);
    }
  };

  // Get unique notification types for filter
  const notificationTypes = Array.from(new Set(notifications?.map(n => n.type) || []));

  return (
    <div className="mx-auto max-w-4xl">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-dark dark:text-white">
              Notifikasi
            </h1>
            <p className="text-sm text-body-color dark:text-dark-6 mt-1">
              {stats.total_count} total notifikasi
              {unreadCount > 0 && `, ${unreadCount} belum dibaca`}
            </p>
          </div>
          
          {hasUnread && (
            <button
              onClick={handleMarkAllAsRead}
              className="rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/90"
            >
              Tandai Semua Dibaca
            </button>
          )}
        </div>
      </div>

      {/* Filters */}
      <div className="mb-6 flex flex-wrap gap-4">
        {/* Read Status Filter */}
        <div className="flex gap-2">
          {[
            { value: 'all', label: 'Semua' },
            { value: 'unread', label: 'Belum Dibaca' },
            { value: 'read', label: 'Sudah Dibaca' }
          ].map((option) => (
            <button
              key={option.value}
              onClick={() => setFilter(option.value as any)}
              className={cn(
                "rounded-lg px-3 py-1.5 text-sm font-medium transition-colors",
                filter === option.value
                  ? "bg-primary text-white"
                  : "bg-gray-2 text-dark hover:bg-gray-3 dark:bg-dark-3 dark:text-white dark:hover:bg-dark-2"
              )}
            >
              {option.label}
            </button>
          ))}
        </div>

        {/* Type Filter */}
        <div className="flex gap-2">
          <button
            onClick={() => setTypeFilter('all')}
            className={cn(
              "rounded-lg px-3 py-1.5 text-sm font-medium transition-colors",
              typeFilter === 'all'
                ? "bg-primary text-white"
                : "bg-gray-2 text-dark hover:bg-gray-3 dark:bg-dark-3 dark:text-white dark:hover:bg-dark-2"
            )}
          >
            Semua Tipe
          </button>
          {notificationTypes.map((type) => (
            <button
              key={type}
              onClick={() => setTypeFilter(type)}
              className={cn(
                "rounded-lg px-3 py-1.5 text-sm font-medium transition-colors",
                typeFilter === type
                  ? "bg-primary text-white"
                  : "bg-gray-2 text-dark hover:bg-gray-3 dark:bg-dark-3 dark:text-white dark:hover:bg-dark-2"
              )}
            >
              {getNotificationIcon(type)} {getNotificationTypeLabel(type)}
            </button>
          ))}
        </div>
      </div>

      {/* Notifications List */}
      <div className="space-y-3">
        {loading && (!notifications || notifications.length === 0) ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-body-color dark:text-dark-6">
              Memuat notifikasi...
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-red-500">
              {error}
            </div>
          </div>
        ) : filteredNotifications.length === 0 ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="text-4xl mb-4">🔔</div>
              <div className="text-body-color dark:text-dark-6">
                {filter === 'all' ? 'Tidak ada notifikasi' : 
                 filter === 'unread' ? 'Tidak ada notifikasi yang belum dibaca' :
                 'Tidak ada notifikasi yang sudah dibaca'}
              </div>
            </div>
          </div>
        ) : (
          <>
            {filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                onClick={() => handleNotificationClick(notification)}
                className={cn(
                  "group relative cursor-pointer rounded-lg border p-4 transition-all hover:shadow-md",
                  notification.is_read
                    ? "border-stroke bg-white dark:border-dark-3 dark:bg-gray-dark"
                    : "border-primary/20 bg-blue-50 dark:border-primary/30 dark:bg-blue-900/20"
                )}
              >
                <div className="flex items-start gap-4">
                  {/* Icon */}
                  <div className="flex-shrink-0 text-2xl">
                    {getNotificationIcon(notification.type)}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className={cn(
                            "text-sm font-medium text-dark dark:text-white",
                            !notification.is_read && "font-semibold"
                          )}>
                            {notification.title}
                          </h3>
                          <span className="rounded-full bg-gray-2 px-2 py-0.5 text-xs text-body-color dark:bg-dark-3 dark:text-dark-6">
                            {getNotificationTypeLabel(notification.type)}
                          </span>
                          {!notification.is_read && (
                            <div className="size-2 rounded-full bg-primary"></div>
                          )}
                        </div>
                        
                        <p className="text-sm text-body-color dark:text-dark-6 mb-2">
                          {notification.message}
                        </p>
                        
                        <span className="text-xs text-body-color dark:text-dark-6">
                          {formatDistanceToNow(new Date(notification.created_at), { 
                            addSuffix: true, 
                            locale: id 
                          })}
                        </span>
                      </div>

                      {/* Delete Button */}
                      <button
                        onClick={(e) => handleDeleteNotification(notification.id, e)}
                        className="opacity-0 group-hover:opacity-100 transition-opacity text-red-500 hover:text-red-700 p-1"
                        title="Hapus notifikasi"
                      >
                        <svg className="size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Load More Button */}
            {notifications && notifications.length >= 20 && (
              <div className="flex justify-center pt-6">
                <button
                  onClick={loadMore}
                  disabled={loadingMore}
                  className="rounded-lg border border-stroke bg-white px-6 py-2 text-sm font-medium text-dark hover:bg-gray-2 disabled:opacity-50 dark:border-dark-3 dark:bg-gray-dark dark:text-white dark:hover:bg-dark-2"
                >
                  {loadingMore ? 'Memuat...' : 'Muat Lebih Banyak'}
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
