'use client';

import Breadcrumb from '@/components/Breadcrumbs/Breadcrumb';

interface ErrorMessageProps {
    title: string;
    message: string;
}

const ErrorMessage = ({ title, message }: ErrorMessageProps) => {
    return (
        <>
            <Breadcrumb pageName={title} />
            <div className="p-4 text-center bg-white dark:bg-gray-dark rounded-md shadow-card">
                {message}
            </div>
        </>
    );
};

export default ErrorMessage;
