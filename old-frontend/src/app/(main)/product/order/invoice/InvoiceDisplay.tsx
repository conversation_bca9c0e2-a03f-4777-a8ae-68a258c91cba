'use client';

import Image from 'next/image';
import Breadcrumb from '@/components/Breadcrumbs/Breadcrumb';
import { Session } from 'next-auth';
import { useState, useEffect } from 'react';
import { ApiClient } from '@/lib/apiClientEnhanced';
import { 
  CreditCard, 
  FileText, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  XCircle,
  Download,
  DollarSign,
  Wallet
} from 'lucide-react';

// Definisikan tipe data untuk invoice
interface Role {
  ID: number;
  Name: string;
  Description?: string;
}

interface User {
  ID: number;
  Name: string;
  Email: string;
  username: string;
  Whatsapp?: string;
  saldo: number;
  Roles: Role[];
}

interface InvoiceData {
  id: number;
  invoice_id: string;
  user_id: number;
  user: User;
  account_id?: number;
  account_type?: string;
  duration?: string;
  type: string;
  description: string;
  amount: number;
  status: string;
  payment_gateway?: string;
  gateway_reference?: string;
  gateway_checkout_url?: string;
  payment_method?: string;
  payment_method_name?: string;
  fee_customer?: number;
  fee_merchant?: number;
  total_fee?: number;
  created_at: string;
  updated_at: string;
}

interface PaymentChannel {
  code: string;
  name: string;
  icon_url: string;
  group: string;
  fee_customer: {
    flat: number;
    percent: number;
  };
  total_fee: {
    flat: number;
    percent: string;
  };
}

interface InvoiceDisplayProps {
    invoice: InvoiceData;
    session: Session;
}

// Fungsi helper untuk status badge
const getStatusBadge = (status: string) => {
  const statusConfig = {
    'PAID': { 
      color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      icon: CheckCircle
    },
    'SUCCESS': { 
      color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      icon: CheckCircle
    },
    'PENDING': { 
      color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      icon: Clock
    },
    'FAILED': { 
      color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      icon: XCircle
    },
    'EXPIRED': { 
      color: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
      icon: AlertCircle
    },
    'CANCELLED': { 
      color: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
      icon: AlertCircle
    }
  };
  
  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig['PENDING'];
  const IconComponent = config.icon;
  
  return (
    <span className={`inline-flex items-center gap-1.5 rounded-full px-3 py-1 text-sm font-medium ${config.color}`}>
      <IconComponent className="h-4 w-4" />
      {status}
    </span>
  );
};

// Ini adalah Client Component murni yang hanya bertugas merender UI.
const InvoiceDisplay = ({ invoice, session }: InvoiceDisplayProps) => {
    const [paymentChannels, setPaymentChannels] = useState<PaymentChannel[]>([]);
    const [isLoadingChannels, setIsLoadingChannels] = useState(true);

    useEffect(() => {
        const fetchPaymentChannels = async () => {
            try {
                const apiClient = new ApiClient();
                const response = await apiClient.get('/payment/channels', {
                    token: session.accessToken,
                });
                setPaymentChannels(response.data || []);
            } catch (error) {
                console.error('Error fetching payment channels:', error);
            } finally {
                setIsLoadingChannels(false);
            }
        };

        if (session?.accessToken) {
            fetchPaymentChannels();
        } else {
            setIsLoadingChannels(false);
        }
    }, [session?.accessToken]);

    // Fungsi untuk mendapatkan informasi payment channel berdasarkan payment_gateway
    const getPaymentChannelInfo = () => {
        if (!invoice.payment_gateway || paymentChannels.length === 0) {
            return null;
        }
        return paymentChannels.find(channel => channel.code === invoice.payment_gateway);
    };

    // Fungsi untuk menghitung fee berdasarkan amount dan channel
    const calculateFee = (channel: PaymentChannel, amount: number) => {
        const flatFee = channel.fee_customer.flat;
        const percentFee = (amount * channel.fee_customer.percent) / 100;
        return flatFee + percentFee;
    };

    const paymentChannelInfo = getPaymentChannelInfo();



    return (
        <>
            <Breadcrumb pageName={`Invoice #${invoice.invoice_id}`} />
            <div className="rounded-[10px] bg-white shadow-1 dark:bg-gray-dark dark:shadow-card">
                <div className="border-b border-stroke px-4 py-6 dark:border-dark-3 sm:px-6 xl:px-9">
                    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                        <div className="flex items-center gap-3">
                            <FileText className="h-8 w-8 text-primary" />
                            <div>
                                <h3 className="text-[22px] font-bold leading-7 text-dark dark:text-white">
                                    Detail Pesanan
                                </h3>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                    Dibuat pada {new Date(invoice.created_at).toLocaleDateString('id-ID', {
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric',
                                        hour: '2-digit',
                                        minute: '2-digit'
                                    })}
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center gap-3">
                            {getStatusBadge(invoice.status)}
                        </div>
                    </div>
                </div>
                <div className="p-4 sm:p-6 xl:p-9">
                    <div className="flex flex-col-reverse gap-5 xl:flex-row xl:justify-between">
                        <div className="flex flex-col gap-4 sm:flex-row xl:gap-9">
                            <div>
                                <p className="mb-1.5 font-medium text-dark dark:text-white">From</p>
                                <h4 className="mb-4 text-[22px] font-medium leading-[30px] text-dark dark:text-white">
                                    {process.env.NEXT_PUBLIC_APP_NAME || 'InsomVPN'}
                                </h4>
                                <a className="block" href="#">
                                    <span className="font-medium">Email: </span>
                                    {process.env.NEXT_PUBLIC_OWNER_EMAIL || '<EMAIL>'}
                                </a>
                                <a className="mt-2 block" href={process.env.NEXT_PUBLIC_OWNER_LINK || '#'} target="_blank" rel="noopener noreferrer">
                                    <span className="font-medium">Website: </span>
                                    {process.env.NEXT_PUBLIC_OWNER_LINK || 'https://insomvpn.com'}
                                </a>
                            </div>
                            <div>
                                <p className="mb-1.5 font-medium text-dark dark:text-white">To</p>
                                <h4 className="mb-4 text-[22px] font-medium leading-[30px] text-dark dark:text-white">
                                    {invoice.user?.Name || session.user?.name || 'Customer'}
                                </h4>
                                <a className="block" href="#">
                                    <span className="font-medium">Email: </span>
                                    {invoice.user?.Email || session.user?.email}
                                </a>
                                {invoice.user?.Whatsapp && (
                                    <span className="mt-2 block">
                                        <span className="font-medium">WhatsApp: </span>
                                        {invoice.user.Whatsapp}
                                    </span>
                                )}
                            </div>
                        </div>
                        <div className="rounded-lg bg-gradient-to-r from-primary to-blue-600 p-6 text-white">
                            <div className="flex items-center gap-3 mb-2">
                                <CreditCard className="h-6 w-6" />
                                <span className="text-sm font-medium opacity-90">Invoice Number</span>
                            </div>
                            <h3 className="text-2xl font-bold mb-1">
                                #{invoice.invoice_id}
                            </h3>
                            <p className="text-sm opacity-90">
                                Total: {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(invoice.amount)}
                            </p>
                        </div>
                    </div>

                    <div className="my-10 rounded-xl border border-stroke bg-gradient-to-r from-gray-50 to-white p-6 shadow-sm dark:border-dark-3 dark:from-gray-800 dark:to-gray-dark">
                        <div className="flex items-center gap-6">
                            <div className="relative">
                                <div className="rounded-xl bg-primary/10 p-4">
                                    <DollarSign className="h-12 w-12 text-primary" />
                                </div>
                                <div className="absolute -top-2 -right-2 rounded-full bg-primary px-2 py-1">
                                    <span className="text-xs font-bold text-white">{invoice.type}</span>
                                </div>
                            </div>
                            <div className="flex-1">
                                <div className="mb-4">
                                    <h4 className="text-lg font-bold text-dark dark:text-white mb-2">
                                        {invoice.type} {invoice.account_type ? `- ${invoice.account_type}` : ''}
                                    </h4>
                                    <p className="text-gray-600 dark:text-gray-300 mb-2">
                                        {invoice.description}
                                    </p>
                                    {invoice.duration && (
                                        <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                                            <Clock className="h-4 w-4" />
                                            <span>Duration: {invoice.duration}</span>
                                        </div>
                                    )}
                                </div>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <span className="text-sm text-gray-500 dark:text-gray-400">Amount:</span>
                                    </div>
                                    <div className="text-right">
                                        <p className="text-2xl font-bold text-primary">
                                            {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(invoice.amount)}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3">
                        <div className="rounded-xl border border-stroke bg-white p-6 shadow-sm dark:border-dark-3 dark:bg-gray-dark">
                            <div className="mb-4 flex items-center gap-3">
                                <div className="rounded-lg bg-blue-100 p-2 dark:bg-blue-900">
                                    <FileText className="h-5 w-5 text-blue-600 dark:text-blue-300" />
                                </div>
                                <h4 className="text-lg font-semibold text-dark dark:text-white">
                                    Transaction Type
                                </h4>
                            </div>
                            <div className="space-y-2">
                                <p className="text-sm text-gray-500 dark:text-gray-400">Type</p>
                                <p className="font-semibold text-dark dark:text-white">{invoice.type}</p>
                                {invoice.account_type && (
                                    <>
                                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-3">Account Type</p>
                                        <p className="font-semibold text-dark dark:text-white">{invoice.account_type}</p>
                                    </>
                                )}
                            </div>
                        </div>
                        <div className="rounded-xl border border-stroke bg-white p-6 shadow-sm dark:border-dark-3 dark:bg-gray-dark">
                            <div className="mb-4 flex items-center gap-3">
                                <div className="rounded-lg bg-green-100 p-2 dark:bg-green-900">
                                    <CreditCard className="h-5 w-5 text-green-600 dark:text-green-300" />
                                </div>
                                <h4 className="text-lg font-semibold text-dark dark:text-white">
                                    Payment Details
                                </h4>
                            </div>
                            <div className="space-y-4">
                                <div>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">Status</p>
                                    <div className="mt-1">{getStatusBadge(invoice.status)}</div>
                                </div>
                                
                                {/* Payment Channel Information - Always show if payment_gateway exists */}
                                <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
                                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">Payment Method</p>
                                    <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
                                        {paymentChannelInfo && !isLoadingChannels ? (
                                            <>
                                                <div className="flex-shrink-0">
                                                    <Image
                                                        src={paymentChannelInfo.icon_url}
                                                        alt={paymentChannelInfo.name}
                                                        width={32}
                                                        height={32}
                                                        className="rounded"
                                                        onError={(e) => {
                                                            const target = e.target as HTMLImageElement;
                                                            target.style.display = 'none';
                                                        }}
                                                    />
                                                </div>
                                                <div className="flex-1">
                                                    <p className="font-semibold text-dark dark:text-white">
                                                        {paymentChannelInfo.name}
                                                    </p>
                                                    <p className="text-xs text-gray-500 dark:text-gray-400">
                                                        {paymentChannelInfo.group} • {paymentChannelInfo.code}
                                                    </p>
                                                </div>
                                            </>
                                        ) : (
                                            <>
                                                <div className="flex-shrink-0">
                                                    <Wallet className="h-8 w-8 text-gray-400" />
                                                </div>
                                                <div className="flex-1">
                                                    <p className="font-semibold text-dark dark:text-white">
                                                        {invoice.payment_gateway || 'Tripay'}
                                                    </p>
                                                    <p className="text-xs text-gray-500 dark:text-gray-400">
                                                        Payment Gateway
                                                    </p>
                                                </div>
                                            </>
                                        )}
                                    </div>
                                    
                                    {/* Fee Information */}
                                    {paymentChannelInfo && !isLoadingChannels && (
                                        <div className="mt-3 p-3 rounded-lg bg-blue-50 dark:bg-blue-900/20">
                                            <p className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">
                                                Fee Information
                                            </p>
                                            <div className="grid grid-cols-2 gap-3 text-sm">
                                                <div>
                                                    <p className="text-gray-600 dark:text-gray-400">Customer Fee</p>
                                                    <p className="font-semibold text-dark dark:text-white">
                                                        {paymentChannelInfo.fee_customer.flat > 0 && 
                                                            `Rp ${new Intl.NumberFormat('id-ID').format(paymentChannelInfo.fee_customer.flat)}`
                                                        }
                                                        {paymentChannelInfo.fee_customer.percent > 0 && 
                                                            `${paymentChannelInfo.fee_customer.flat > 0 ? ' + ' : ''}${paymentChannelInfo.fee_customer.percent}%`
                                                        }
                                                        {paymentChannelInfo.fee_customer.flat === 0 && paymentChannelInfo.fee_customer.percent === 0 && 'Free'}
                                                    </p>
                                                </div>
                                                <div>
                                                    <p className="text-gray-600 dark:text-gray-400">Estimated Fee</p>
                                                    <p className="font-semibold text-primary">
                                                        Rp {new Intl.NumberFormat('id-ID').format(calculateFee(paymentChannelInfo, invoice.amount))}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                                
                                {invoice.gateway_reference && (
                                    <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
                                        <p className="text-sm text-gray-500 dark:text-gray-400">Reference</p>
                                        <p className="font-mono text-sm text-dark dark:text-white break-all">{invoice.gateway_reference}</p>
                                    </div>
                                )}
                            </div>
                        </div>
                        <div className="rounded-xl border border-stroke bg-white p-6 shadow-sm dark:border-dark-3 dark:bg-gray-dark md:col-span-2 xl:col-span-1">
                            <div className="mb-4 flex items-center gap-3">
                                <div className="rounded-lg bg-purple-100 p-2 dark:bg-purple-900">
                                    <DollarSign className="h-5 w-5 text-purple-600 dark:text-purple-300" />
                                </div>
                                <h4 className="text-lg font-semibold text-dark dark:text-white">
                                    Summary
                                </h4>
                            </div>
                            <div className="space-y-4">
                                <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                                    <span className="text-sm text-gray-500 dark:text-gray-400">Transaction Type</span>
                                    <span className="font-medium text-dark dark:text-white">{invoice.type}</span>
                                </div>
                                <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                                    <span className="text-sm text-gray-500 dark:text-gray-400">Status</span>
                                    <div>{getStatusBadge(invoice.status)}</div>
                                </div>
                                

                                
                                <div className="flex justify-between items-center py-3 border-t-2 border-primary">
                                    <span className="text-lg font-semibold text-dark dark:text-white">Invoice Amount</span>
                                    <span className="text-xl font-bold text-primary">
                                        {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(invoice.amount)}
                                    </span>
                                </div>
                            </div>
                            <div className="mt-6 space-y-3">
                                <button className="w-full flex items-center justify-center gap-2 rounded-lg border border-primary px-4 py-3 text-center font-medium text-primary transition-colors hover:bg-primary hover:text-white dark:hover:border-primary">
                                    <Download className="h-5 w-5" />
                                    Download Invoice
                                </button>
                                {invoice.status === 'PENDING' && invoice.gateway_checkout_url && (
                                    <a 
                                        href={invoice.gateway_checkout_url} 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className="w-full flex items-center justify-center gap-2 rounded-lg bg-gradient-to-r from-primary to-blue-600 px-4 py-3 text-center font-medium text-white transition-all hover:from-primary/90 hover:to-blue-600/90 hover:shadow-lg"
                                    >
                                        <CreditCard className="h-5 w-5" />
                                        Proceed to Payment
                                    </a>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default InvoiceDisplay;
