import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createAuthenticatedApiClient } from '@/lib/apiClientEnhanced';
import InvoiceDisplay from './InvoiceDisplay';
import ErrorMessage from './ErrorMessage';
import { Metadata, ResolvingMetadata } from 'next';

type Props = {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};

export async function generateMetadata(
  { searchParams }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const resolvedSearchParams = await searchParams;
  const invoice_id = resolvedSearchParams.invoice_id || '';

  return {
    title: `Detail Invoice ${invoice_id} | ${process.env.NEXT_PUBLIC_APP_NAME}`,
    description: `Detail invoice untuk transaksi Anda dengan ID ${invoice_id}.`,
  };
}

export const dynamic = 'force-dynamic';

interface User {
  ID: number;
  Name: string;
  Email: string;
  username: string;
  Whatsapp?: string;
  saldo: number;
  Roles: Role[];
}

interface Role {
  ID: number;
  Name: string;
  Description?: string;
}

interface InvoiceData {
  id: number;
  invoice_id: string;
  user_id: number;
  user: User;
  account_id?: number;
  account_type?: string;
  duration?: string;
  type: string;
  description: string;
  amount: number;
  status: string;
  payment_gateway?: string;
  gateway_reference?: string;
  gateway_checkout_url?: string;
  created_at: string;
  updated_at: string;
}

async function getInvoiceData(invoiceId: string, accessToken: string): Promise<InvoiceData> {
  const apiClient = await createAuthenticatedApiClient();
  return await apiClient.get(`/users/me/invoices/${invoiceId}`);
}

const InvoicePage = async ({ searchParams }: Props) => {
  const resolvedSearchParams = await searchParams;
  const invoiceId = resolvedSearchParams.invoice_id;

  if (!invoiceId || typeof invoiceId !== 'string') {
    return <ErrorMessage title="Error" message="Invoice ID tidak ditemukan atau tidak valid di URL." />;
  }

  const session = await getServerSession(authOptions);
  if (!session?.accessToken) {
    return <ErrorMessage title="Akses Ditolak" message="Anda harus login untuk melihat halaman ini." />;
  }

  try {
    const invoiceData = await getInvoiceData(invoiceId, session.accessToken);
    return <InvoiceDisplay invoice={invoiceData} session={session} />;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Terjadi kesalahan tidak diketahui.';
    return <ErrorMessage title="Gagal Memuat Invoice" message={errorMessage} />;
  }
};

export default InvoicePage;
