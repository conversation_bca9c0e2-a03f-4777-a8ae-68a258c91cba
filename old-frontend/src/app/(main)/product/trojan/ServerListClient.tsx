'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useNotification } from '@/contexts/NotificationContext';
import { ApiClient } from '@/lib/apiClientEnhanced';
import Link from 'next/link';
import { Server } from '@/types/server';

interface ServerListClientProps {
  servers: Server[];
}

// Modern Icons
const TestTubeIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={18}
    height={18}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2" />
    <line x1="8" y1="2" x2="16" y2="2" />
    <line x1="8" y1="8" x2="12" y2="8" />
  </svg>
);

const ShieldIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
  </svg>
);

const SpeedIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
  </svg>
);

const LocationIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
);

const UsersIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
  </svg>
);

const ServerListClient = ({ servers }: ServerListClientProps) => {
  const [activeLocation, setActiveLocation] = useState('semua');
  const { data: session } = useSession();
  const { addNotification } = useNotification();
  const [testingServerId, setTestingServerId] = useState<number | null>(null);

  const locationFilteredServers = activeLocation === 'semua'
    ? servers
    : servers.filter(server => server.negara.toLowerCase() === activeLocation);

  const handleTestToken = async (serverId: number) => {
    if (testingServerId === serverId) return;
    setTestingServerId(serverId);

    try {
      if (!session?.accessToken) {
        addNotification('Sesi tidak valid atau Anda tidak terautentikasi.', 'error');
        return;
      }

      const apiClient = new ApiClient();
      const data = await apiClient.get(`/servers/${serverId}/test-token`, { token: session.accessToken });
      addNotification(data.message, 'success');
    } catch (error: any) {
      console.error('Error fatal saat menguji token:', error);
      addNotification(error.message || 'Terjadi kesalahan jaringan.', 'error');
    } finally {
      setTestingServerId(null);
    }
  };

  // Get location stats
  const locationStats = {
    total: servers.length,
    indonesia: servers.filter(s => s.negara.toLowerCase() === 'indonesia').length,
    singapore: servers.filter(s => s.negara.toLowerCase() === 'singapore').length,
  };

  if (servers.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
        <div className="w-24 h-24 bg-gradient-to-br from-pink-100 to-purple-100 dark:from-pink-900/20 dark:to-purple-900/20 rounded-full flex items-center justify-center mb-6">
          <ShieldIcon />
        </div>
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Tidak Ada Server Tersedia</h3>
        <p className="text-gray-600 dark:text-gray-400 max-w-md">
          Saat ini tidak ada server Trojan yang tersedia atau terjadi kesalahan saat memuat data. Silakan coba lagi nanti.
        </p>
      </div>
    );
  }

  return (
    <div className="w-full space-y-8">
      {/* Hero Section */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-pink-500 via-purple-500 to-indigo-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
                  <ShieldIcon />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">Trojan Premium Servers</h1>
                  <p className="text-white/80">Keamanan tingkat enterprise dengan performa maksimal</p>
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-4 max-w-md">
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center">
                  <div className="text-2xl font-bold">{locationStats.total}</div>
                  <div className="text-xs text-white/80">Total Server</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center">
                  <div className="text-2xl font-bold">{locationStats.indonesia}</div>
                  <div className="text-xs text-white/80">Indonesia</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center">
                  <div className="text-2xl font-bold">{locationStats.singapore}</div>
                  <div className="text-xs text-white/80">Singapore</div>
                </div>
              </div>
            </div>
            
            <div className="hidden lg:block">
              <div className="w-32 h-32 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center">
                <div className="text-6xl">🛡️</div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
      </div>

      {/* Filter Section */}
      <div className="bg-white dark:bg-gray-dark rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-dark-3">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-1">Pilih Lokasi Server</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">Filter server berdasarkan lokasi geografis</p>
          </div>
          
          <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-xl p-1.5">
            <button
              onClick={() => setActiveLocation('semua')}
              className={`flex items-center space-x-2 px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200 ${
                activeLocation === 'semua'
                  ? 'bg-white dark:bg-gray-600 text-pink-600 dark:text-pink-400 shadow-md transform scale-105'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              <span className="text-lg">🌍</span>
              <span>Semua ({locationStats.total})</span>
            </button>
            <button
              onClick={() => setActiveLocation('indonesia')}
              className={`flex items-center space-x-2 px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200 ${
                activeLocation === 'indonesia'
                  ? 'bg-white dark:bg-gray-600 text-pink-600 dark:text-pink-400 shadow-md transform scale-105'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              <span className="text-lg">🇮🇩</span>
              <span>Indonesia ({locationStats.indonesia})</span>
            </button>
            <button
              onClick={() => setActiveLocation('singapore')}
              className={`flex items-center space-x-2 px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200 ${
                activeLocation === 'singapore'
                  ? 'bg-white dark:bg-gray-600 text-pink-600 dark:text-pink-400 shadow-md transform scale-105'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              <span className="text-lg">🇸🇬</span>
              <span>Singapore ({locationStats.singapore})</span>
            </button>
          </div>
        </div>
      </div>

      {/* Server Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {locationFilteredServers.length > 0 ? (
          locationFilteredServers.map(server => {
            const availableSlots = server.slot_server - server.total_user;
            const usagePercentage = (server.total_user / server.slot_server) * 100;
            
            return (
              <article 
                 key={server.server_id} 
                 className="group relative bg-white dark:bg-gray-dark rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 border border-gray-200 dark:border-dark-3 overflow-hidden"
               >
                {/* Card Header */}
                <div className="relative p-6 bg-gradient-to-br from-pink-50 to-purple-50 dark:from-pink-900/20 dark:to-purple-900/20">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gradient-to-br from-pink-500 to-purple-500 rounded-xl flex items-center justify-center text-white shadow-lg">
                        <ShieldIcon />
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-gray-900 dark:text-white group-hover:text-pink-600 dark:group-hover:text-pink-400 transition-colors">
                          {server.nama}
                        </h3>
                        <div className="flex items-center space-x-2 mt-1">
                          <LocationIcon />
                          <span className="text-sm text-gray-600 dark:text-gray-400 capitalize">{server.negara}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-bold bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300">
                        TROJAN
                      </span>
                    </div>
                  </div>
                </div>

                {/* Card Body */}
                <div className="p-6 space-y-4">
                  {/* Pricing */}
                  <div className="space-y-2">
                    <div className="flex items-baseline space-x-2">
                      <span className="text-2xl font-bold text-gray-900 dark:text-white">
                        {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(server.harga_member)}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">/bulan</span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Reseller: <span className="font-semibold">{new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(server.harga_reseller)}</span>
                    </p>
                  </div>

                  {/* Server Info */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-2 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                          <span className="text-sm">🏢</span>
                        </div>
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Provider</span>
                      </div>
                      <span className="text-sm font-bold text-gray-900 dark:text-white">{server.nama_isp}</span>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <UsersIcon />
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Kapasitas</span>
                        </div>
                        <span className="text-sm font-bold text-gray-900 dark:text-white">
                          {availableSlots} / {server.slot_server}
                        </span>
                      </div>
                      
                      <div className="w-full bg-gray-200 dark:bg-dark-2 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full transition-all duration-300 ${
                            usagePercentage > 80 ? 'bg-red-500' : usagePercentage > 60 ? 'bg-yellow-500' : 'bg-green-500'
                          }`}
                          style={{ width: `${usagePercentage}%` }}
                        ></div>
                      </div>
                      
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-gray-500 dark:text-gray-400">Terpakai: {server.total_user}</span>
                        <span className={`font-medium ${
                          availableSlots > 10 ? 'text-green-600 dark:text-green-400' : 
                          availableSlots > 5 ? 'text-yellow-600 dark:text-yellow-400' : 
                          'text-red-600 dark:text-red-400'
                        }`}>
                          {availableSlots > 0 ? `${availableSlots} slot tersisa` : 'Penuh'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Performance Indicators */}
                  <div className="grid grid-cols-3 gap-2 pt-2">
                    <div className="flex items-center space-x-1 text-xs text-green-600 dark:text-green-400">
                      <SpeedIcon />
                      <span>Cepat</span>
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-blue-600 dark:text-blue-400">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                      <span>Aman</span>
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-purple-600 dark:text-purple-400">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      <span>Stabil</span>
                    </div>
                  </div>
                </div>

                {/* Card Footer */}
                <div className="p-6 pt-0">
                  <div className="flex items-center space-x-3">
                    <button 
                      onClick={() => handleTestToken(server.server_id)}
                      className="flex-1 inline-flex items-center justify-center space-x-2 px-4 py-2.5 rounded-xl border-2 border-pink-200 dark:border-pink-800 text-pink-600 dark:text-pink-400 hover:bg-pink-50 dark:hover:bg-pink-900/20 font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                      disabled={testingServerId === server.server_id}
                    >
                      {testingServerId === server.server_id ? (
                        <>
                          <div className="w-4 h-4 border-2 border-pink-600 border-t-transparent rounded-full animate-spin"></div>
                          <span>Testing...</span>
                        </>
                      ) : (
                        <>
                          <TestTubeIcon />
                          <span>Test</span>
                        </>
                      )}
                    </button>
                    
                    <Link 
                      href={`/product/trojan/${server.server_id}`} 
                      className="flex-1 inline-flex items-center justify-center space-x-2 px-4 py-2.5 rounded-xl bg-gradient-to-r from-pink-500 to-purple-500 text-white font-medium hover:from-pink-600 hover:to-purple-600 transition-all duration-200 shadow-lg hover:shadow-xl"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span>Detail</span>
                    </Link>
                  </div>
                </div>
              </article>
            );
          })
        ) : (
          <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
             <div className="w-20 h-20 bg-gray-100 dark:bg-dark-2 rounded-full flex items-center justify-center mb-4">
              <LocationIcon />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Tidak Ada Server</h3>
            <p className="text-gray-600 dark:text-gray-400 max-w-md">
              Tidak ada server Trojan yang tersedia untuk lokasi <span className="font-semibold capitalize">{activeLocation}</span>. 
              Coba pilih lokasi lain atau kembali lagi nanti.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ServerListClient;
