import { Metadata } from 'next';
import { Server } from '@/types/server';
import { createAuthenticatedApiClient } from '@/lib/apiClientEnhanced';
import OrderDetailClient from './OrderDetailClient';
import { notFound } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

export const dynamic = 'force-dynamic';

interface OrderDetailPageProps {
  params: Promise<{ id: string }>;
}

async function fetchServer(id: string, token: string | undefined): Promise<{ server: Server | null; error?: string }> {
  if (!token) {
    return { server: null, error: 'Sesi otentikasi tidak ditemukan.' };
  }

  try {
    const apiClient = await createAuthenticatedApiClient();
    const data = await apiClient.get(`/servers/${id}`);
    return { server: data };

  } catch (err: any) {
    console.error("Error fetching server:", err);
    return { server: null, error: err.message || '<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han pada server.' };
  }
}

export async function generateMetadata({ params }: OrderDetailPageProps): Promise<Metadata> {
  const { id } = await params;
  const session = await getServerSession(authOptions);
  const { server } = await fetchServer(id, session?.accessToken);

  if (!server) {
    return {
      title: 'Server Tidak Ditemukan',
    };
  }

  return {
    title: `Detail Server: ${server.nama}`,
    description: `Pesan layanan Vless di server ${server.nama} (${server.negara}).`,
  };
}

const OrderDetailPage = async ({ params }: OrderDetailPageProps) => {
  const { id } = await params;
  const session = await getServerSession(authOptions);
  const { server, error } = await fetchServer(id, session?.accessToken);

  if (!server) {
    notFound();
  }

  return <OrderDetailClient server={server} error={error} />;
};

export default OrderDetailPage;