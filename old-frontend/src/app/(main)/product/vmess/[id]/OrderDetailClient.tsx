'use client';

import Breadcrumb from '@/components/Breadcrumbs/Breadcrumb';
import { ShowcaseSection } from '@/components/Layouts/showcase-section';
import { Button } from '@/components/ui-elements/button';
import InputGroup from '@/components/FormElements/InputGroup';
import { useNotification } from '@/contexts/NotificationContext';
import { Server } from '@/types/server';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useBalance } from '@/contexts/BalanceContext';
import { usePaymentChannels, groupChannelsByType, formatFee } from '@/hooks/use-payment-channels';

// Add custom styles for animations
const styles = `
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out;
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}

interface DurationOption {
  duration: number;
  price: number;
  unit: 'hour' | 'month';
  label: string;
}

interface OrderDetailClientProps {
  server: Server | null;
  error?: string;
}

type TabType = 'monthly' | 'hourly';

const OrderDetailClient = ({ server, error }: OrderDetailClientProps) => {
  const router = useRouter();

  // Derive duration options for monthly subscriptions
  const monthlyOptions: DurationOption[] = server
    ? [
        { duration: 1, price: server.harga_member * 1, unit: 'month', label: '1 Bulan' },
        { duration: 2, price: server.harga_member * 2, unit: 'month', label: '2 Bulan' },
        { duration: 3, price: server.harga_member * 3, unit: 'month', label: '3 Bulan' },
        { duration: 4, price: server.harga_member * 4, unit: 'month', label: '4 Bulan' },
        { duration: 5, price: server.harga_member * 5, unit: 'month', label: '5 Bulan' },
        { duration: 6, price: server.harga_member * 6, unit: 'month', label: '6 Bulan' },
      ]
    : [];

  // State management
  const [activeTab, setActiveTab] = useState<TabType>('monthly');
  const [selectedDuration, setSelectedDuration] = useState<DurationOption | null>(
    monthlyOptions[0] ?? null,
  );
  const [username, setUsername] = useState('');
  const [paymentMethod, setPaymentMethod] = useState<'SALDO' | 'TRIPAY'>('SALDO');
  const [tripayMethod, setTripayMethod] = useState('');
  const [isPurchasing, setIsPurchasing] = useState({
    monthly: false,
    hourly: false,
    trial: false
  });
  const [isWaitingPayment, setIsWaitingPayment] = useState(false);
  const [paymentOrderId, setPaymentOrderId] = useState<string | null>(null);

  const { addNotification } = useNotification();
  const { balance, isLoading, refreshBalance } = useBalance();
  const { channels, isLoading: channelsLoading } = usePaymentChannels();

  // Calculate hourly price (assuming it's per hour based on monthly price)
  const hourlyPrice = server ? Math.ceil(server.harga_member / (30 * 24)) : 0;

  useEffect(() => {
    if (server) {
      document.title = `Detail Server: ${server.nama}`;
    } else if (error) {
      document.title = 'Error Memuat Server';
    }
  }, [server, error]);

  // Set default tripay method when channels are loaded
  useEffect(() => {
    if (channels.length > 0 && !tripayMethod) {
      // Set default ke QRIS jika ada, atau channel pertama
      const qrisChannel = channels.find(ch => ch.code === 'QRIS' || ch.code === 'QRIS2');
      const defaultChannel = qrisChannel || channels[0];
      setTripayMethod(defaultChannel.code);
    }
  }, [channels, tripayMethod]);

  // Function untuk polling status pembayaran
  const pollPaymentStatus = async (orderId: string, username: string, serverCode: string) => {
    const maxAttempts = 60; // 5 menit (60 x 5 detik)
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;

        const response = await fetch(`/api/payment/status/${orderId}`);
        const data = await response.json();

        // Cek format response dari backend - status ada di data.status
        const status = data.data?.status;

        if (status === 'PAID') {
          // Payment berhasil, redirect ke AccountDetailClient dengan username dan server code
          setIsWaitingPayment(false);
          addNotification('Pembayaran berhasil! Mengalihkan ke halaman detail akun...', 'success');
          refreshBalance();

          // Redirect ke halaman detail akun dengan username dan server code
          router.push(`/detail/${username}/${serverCode}`);
          return;
        } else if (status === 'FAILED' || status === 'EXPIRED' || status === 'CANCELLED') {
          // Payment gagal
          setIsWaitingPayment(false);
          addNotification(`Pembayaran ${status.toLowerCase()}. Silakan coba lagi.`, 'error');
          return;
        }

        // Jika masih pending, lanjutkan polling
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll setiap 5 detik
        } else {
          // Timeout
          setIsWaitingPayment(false);
          addNotification('Timeout menunggu konfirmasi pembayaran. Silakan cek status di halaman riwayat.', 'warning');
        }
      } catch (error) {
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setIsWaitingPayment(false);
          addNotification('Gagal memantau status pembayaran. Silakan cek status di halaman riwayat.', 'error');
        }
      }
    };

    poll();
  };



  const handlePurchase = async (type: 'monthly' | 'trial' | 'hourly') => {
    setIsPurchasing(prev => ({ ...prev, [type]: true }));

    if (type === 'monthly') {
      // Validasi saldo hanya jika menggunakan metode SALDO
      if (paymentMethod === 'SALDO') {
        const userBalance = balance ?? 0;
        if (userBalance < (selectedDuration?.price ?? 0)) {
          addNotification('Saldo Anda tidak mencukupi untuk melakukan pembelian ini. Silakan pilih metode pembayaran lain atau top up saldo.', 'error');
          setIsPurchasing(prev => ({ ...prev, [type]: false }));
          return;
        }
      }

      if (!username.trim()) {
        addNotification('Username harus diisi untuk langganan bulanan.', 'error');
        setIsPurchasing(prev => ({ ...prev, [type]: false }));
        return;
      }
      if (username.trim().length < 6) {
        addNotification('Username harus minimal 6 karakter.', 'error');
        setIsPurchasing(prev => ({ ...prev, [type]: false }));
        return;
      }
      if (username.trim().length > 12) {
        addNotification('Username maksimal 12 karakter.', 'error');
        setIsPurchasing(prev => ({ ...prev, [type]: false }));
        return;
      }
      if (!selectedDuration) {
        addNotification('Silakan pilih durasi langganan.', 'error');
        setIsPurchasing(prev => ({ ...prev, [type]: false }));
        return;
      }
    }

    if (type === 'hourly') {
      if (!username.trim()) {
        addNotification('Username harus diisi untuk akun per jam.', 'error');
        setIsPurchasing(prev => ({ ...prev, [type]: false }));
        return;
      }
      if (username.trim().length < 6) {
        addNotification('Username harus minimal 6 karakter.', 'error');
        setIsPurchasing(prev => ({ ...prev, [type]: false }));
        return;
      }
      if (username.trim().length > 12) {
        addNotification('Username maksimal 12 karakter.', 'error');
        setIsPurchasing(prev => ({ ...prev, [type]: false }));
        return;
      }
    }

    let payload: any;
    
    if (type === 'hourly') {
      // Untuk hourly, gunakan endpoint khusus dengan struktur yang berbeda
      payload = {
        kode_server: server?.kode,
        protocol: 'vmess',
        username: username,
      };
    } else {
      // Untuk monthly dan trial, gunakan struktur lama
      payload = {
        kode_server: server?.kode,
        account_type: 'vmess',
        subscription_type: type,
        durasi: type === 'trial' ? 1 : selectedDuration!.duration,
        username: type === 'trial' ? '' : username,
        // Tambahkan metode pembayaran untuk monthly
        ...(type === 'monthly' && {
          payment_method: paymentMethod,
          tripay_method: paymentMethod === 'TRIPAY' ? tripayMethod : undefined
        })
      };
    }

    try {
      const endpoint = '/api/purchase'; // Semua jenis purchase menggunakan endpoint yang sama
      const res = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await res.json();

      if (res.ok) {
        // Refresh balance untuk semua jenis pembelian
        refreshBalance();

        // Cek apakah ada informasi redirect dari API
        const redirectInfo = data?.redirect_info;
        const username = redirectInfo?.username || data?.username || data?.account?.username || data?.data?.username;

        if (type === 'trial') {
          addNotification('Akun trial berhasil dibuat!', 'success');
          if (username && server?.kode) {
            // Arahkan ke detail akun trial
            router.push(`/detail/${username}/${server.kode}`);
          } else {
            // Fallback ke riwayat jika tidak ada username
            router.push('/history');
          }
        } else if (type === 'hourly') {
          addNotification('Akun per jam berhasil dibuat!', 'success');
          if (username && server?.kode) {
            // Arahkan ke detail akun hourly
            router.push(`/detail/${username}/${server.kode}`);
          } else {
            // Fallback ke riwayat jika tidak ada username
            router.push('/history');
          }
        } else {
          // Untuk pembelian bulanan, cek apakah ada checkout_url atau order_id
          const checkoutUrl = data?.checkout_url;
          const orderId = data?.order_id || data?.data?.order_id;

          if (checkoutUrl) {
            // Pembayaran via Tripay - buka payment gateway dan mulai polling
            const reference = data?.gateway_reference || data?.reference;

            // Buka payment gateway di tab baru
            window.open(checkoutUrl, '_blank');

            // Mulai polling status pembayaran menggunakan gateway reference
            if (reference && username && server?.kode) {
              setIsWaitingPayment(true);
              setPaymentOrderId(reference);
              addNotification('Pembayaran berhasil dibuat! Memantau status pembayaran...', 'success');
              pollPaymentStatus(reference, username, server.kode);
            } else {
              addNotification('Pembayaran berhasil dibuat. Silakan cek status di halaman riwayat setelah pembayaran selesai.', 'info');
            }
          } else if (orderId || username) {
            // Pembayaran saldo berhasil
            addNotification('Pembelian berhasil!', 'success');
            if (username && server?.kode) {
              // Arahkan ke detail akun monthly
              router.push(`/detail/${username}/${server.kode}`);
            } else {
              // Fallback ke riwayat
              router.push('/history');
            }
          } else {
            // Fallback ke riwayat
            addNotification('Pembelian berhasil, mengalihkan ke riwayat.', 'success');
            router.push('/history');
          }
        }
      } else {
        // Tampilkan pesan error yang lebih detail dari backend
        let errorMessage = 'Terjadi kesalahan saat melakukan pembelian.';

        if (data.error) {
          errorMessage = data.error;

          // Jika ada detail error (untuk validation errors)
          if (data.details && Array.isArray(data.details)) {
            const detailMessages = data.details.map((detail: any) => {
              if (typeof detail === 'string') return detail;
              if (detail.message) return detail.message;
              if (detail.field && detail.error) return `${detail.field}: ${detail.error}`;
              return JSON.stringify(detail);
            });
            errorMessage = `${errorMessage}\n${detailMessages.join('\n')}`;
          }
        } else if (data.message) {
          errorMessage = data.message;
        }

        addNotification(errorMessage, 'error');
      }
    } catch (error) {
      console.error('Purchase error:', error);
      addNotification('Tidak dapat terhubung ke server. Silakan coba lagi nanti.', 'error');
    } finally {
      setIsPurchasing(prev => ({ ...prev, [type]: false }));
    }
  };

  if (error) {
    return (
      <>
        <Breadcrumb pageName="Error" />
        <div className="text-center text-red-500">Error: {error}</div>
      </>
    );
  }

  if (!server) {
    return (
        <>
            <Breadcrumb pageName="Tidak Ditemukan" />
            <div className="text-center">Server tidak ditemukan atau data tidak lengkap.</div>
        </>
    );
  }

  return (
    <>
      <Breadcrumb pageName={`Detail Server: ${server.nama}`} />

      {/* Payment Waiting Status */}
      {isWaitingPayment && (
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-center gap-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <div>
              <h4 className="font-semibold text-blue-900 dark:text-blue-100">
                Menunggu Konfirmasi Pembayaran
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Silakan selesaikan pembayaran di tab yang telah dibuka.
                Halaman ini akan otomatis refresh setelah pembayaran berhasil.
              </p>
              <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                Setelah pembayaran selesai, Anda akan otomatis diarahkan ke halaman akun.
              </p>
              {paymentOrderId && (
                <div className="mt-2 text-xs text-blue-500 dark:text-blue-400">
                  Order ID: {paymentOrderId}
                </div>
              )}

            </div>
          </div>
        </div>
      )}



      <div className="grid grid-cols-1 gap-9 md:grid-cols-2">
        <ShowcaseSection title="Detail Server">
          <div className="space-y-6 p-6">
            {/* Server Status Header */}
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-xl border border-green-200 dark:border-green-700">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="w-4 h-4 bg-green-500 rounded-full animate-pulse"></div>
                  <div className="absolute inset-0 w-4 h-4 bg-green-500 rounded-full animate-ping opacity-75"></div>
                </div>
                <div>
                  <h3 className="font-bold text-gray-900 dark:text-white">{server.nama}</h3>
                  <p className="text-sm text-green-600 dark:text-green-400 font-medium">🟢 Server Online</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl mb-1">🛡️</div>
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                  VMESS
                </span>
              </div>
            </div>

            {/* Server Information Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Location Card */}
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                    <span className="text-xl">🌍</span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Lokasi Server</p>
                    <p className="font-bold text-gray-900 dark:text-white">{server.negara}</p>
                  </div>
                </div>
              </div>

              {/* Provider Card */}
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                    <span className="text-xl">🏢</span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Provider</p>
                    <p className="font-bold text-gray-900 dark:text-white">{server.nama_isp}</p>
                  </div>
                </div>
              </div>

              {/* Hostname Card */}
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                    <span className="text-xl">🔗</span>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-600 dark:text-gray-400">Hostname</p>
                    <span className="font-mono text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-gray-900 dark:text-white break-all">
                      {server.domain.slice(0, -6)}••••••
                    </span>
                  </div>
                </div>
              </div>

              {/* Capacity Card */}
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
                    <span className="text-xl">👥</span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Kapasitas</p>
                    <div className="flex items-center space-x-2">
                      <p className="font-bold text-gray-900 dark:text-white">
                        {server.slot_server - server.total_user} / {server.slot_server}
                      </p>
                      <span className="text-xs text-green-600 dark:text-green-400">tersedia</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Performance Indicators */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700">
              <h4 className="font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                <span className="text-xl mr-2">⚡</span>
                Performa Server
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <span className="text-green-500">✓</span>
                  <span className="text-gray-700 dark:text-gray-300">Kecepatan Tinggi</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-green-500">✓</span>
                  <span className="text-gray-700 dark:text-gray-300">Optimized Streaming</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-green-500">✓</span>
                  <span className="text-gray-700 dark:text-gray-300">Secure Browsing</span>
                </div>
              </div>
              <div className="mt-4 p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                  🚀 Server dengan infrastruktur premium yang dioptimalkan khusus untuk protokol VMess, 
                  memberikan keamanan maksimal dan performa terbaik untuk streaming dan browsing.
                </p>
              </div>
            </div>
          </div>
        </ShowcaseSection>

        <div className="flex flex-col gap-9">
          <ShowcaseSection title="Paket Langganan">
            <div className="space-y-6 p-6">
              {/* Tab Navigation */}
              <div className="flex bg-gray-100 dark:bg-gray-800 rounded-xl p-1">
                <button
                  onClick={() => setActiveTab('monthly')}
                  className={`flex-1 py-3 px-6 rounded-lg font-semibold transition-all duration-300 ${
                    activeTab === 'monthly'
                      ? 'bg-white dark:bg-gray-700 text-primary shadow-md transform scale-105'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                  }`}
                >
                  📅 Paket Bulanan
                </button>
                <button
                  onClick={() => setActiveTab('hourly')}
                  className={`flex-1 py-3 px-6 rounded-lg font-semibold transition-all duration-300 ${
                    activeTab === 'hourly'
                      ? 'bg-white dark:bg-gray-700 text-primary shadow-md transform scale-105'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                  }`}
                >
                  ⏰ Paket Per Jam
                </button>
              </div>

              {/* Username Input */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Username
                </label>
                <input
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="Masukkan username yang Anda inginkan (6-12 karakter)"
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 ${
                    username && (username.length < 6 || username.length > 12)
                      ? 'border-red-500 dark:border-red-400'
                      : 'border-gray-300 dark:border-gray-600'
                  }`}
                />
                {username && (username.length < 6 || username.length > 12) && (
                  <p className="text-sm text-red-600 dark:text-red-400">
                    {username.length < 6
                      ? `Username terlalu pendek (${username.length}/6 karakter minimum)`
                      : `Username terlalu panjang (${username.length}/12 karakter maksimum)`
                    }
                  </p>
                )}
                {username && username.length >= 6 && username.length <= 12 && (
                  <p className="text-sm text-green-600 dark:text-green-400">
                    ✓ Username valid ({username.length}/12 karakter)
                  </p>
                )}
              </div>






              {/* Tab Content */}
              <div className="min-h-[400px]">
                {activeTab === 'monthly' && (
                  <div className="space-y-6 animate-fadeIn">
                    <div className="text-center">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                        Pilih Durasi Langganan
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        Semakin lama berlangganan, semakin hemat!
                      </p>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {monthlyOptions.map((option) => (
                        <button
                          key={`${option.duration}-${option.unit}`}
                          onClick={() => setSelectedDuration(option)}
                          className={`relative p-4 rounded-xl border-2 transition-all duration-300 hover:scale-105 ${
                            selectedDuration?.duration === option.duration && selectedDuration?.unit === option.unit
                              ? 'border-primary bg-gradient-to-br from-primary/10 to-primary/20 shadow-lg'
                              : 'border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 hover:border-primary/50'
                          }`}
                        >
                          <div className="text-center">
                            <div className="text-lg font-bold text-gray-900 dark:text-white">
                              {option.label}
                            </div>
                            <div className="text-sm text-primary font-semibold mt-1">
                              Rp {option.price.toLocaleString('id-ID')}
                            </div>
                            {option.duration >= 3 && (
                              <div className="text-xs text-green-600 dark:text-green-400 mt-1">
                                💰 Hemat!
                              </div>
                            )}
                          </div>
                          {selectedDuration?.duration === option.duration && selectedDuration?.unit === option.unit && (
                            <div className="absolute -top-2 -right-2 w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                          )}
                        </button>
                      ))}
                    </div>

                    <div className="bg-gradient-to-r from-primary/10 to-blue-500/10 rounded-xl p-6 text-center">
                      <h4 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                        {selectedDuration ? `Rp ${selectedDuration.price.toLocaleString('id-ID')}` : 'Pilih Paket'}
                      </h4>
                      <p className="text-gray-600 dark:text-gray-400">
                        {selectedDuration ? `Untuk ${selectedDuration.label}` : 'Silakan pilih durasi langganan'}
                      </p>
                    </div>

                    {/* Payment Method Selection */}
                    <div className="space-y-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
                      <h4 className="font-semibold text-gray-900 dark:text-white">Metode Pembayaran</h4>

                      {/* Payment Method Options */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <button
                          onClick={() => setPaymentMethod('SALDO')}
                          className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                            paymentMethod === 'SALDO'
                              ? 'border-primary bg-primary/10 text-primary'
                              : 'border-gray-200 dark:border-gray-600 hover:border-primary/50'
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
                              <span className="text-lg">💰</span>
                            </div>
                            <div className="text-left">
                              <div className="font-semibold text-gray-900 dark:text-white">Saldo Akun</div>
                              <div className="text-sm text-gray-600 dark:text-gray-400">
                                Bayar menggunakan saldo yang tersedia
                              </div>
                              <div className="text-sm font-medium text-orange-600 dark:text-orange-400">
                                Saldo: Rp {(balance ?? 0).toLocaleString('id-ID')}
                              </div>
                            </div>
                          </div>
                        </button>

                        <button
                          onClick={() => setPaymentMethod('TRIPAY')}
                          className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                            paymentMethod === 'TRIPAY'
                              ? 'border-primary bg-primary/10 text-primary'
                              : 'border-gray-200 dark:border-gray-600 hover:border-primary/50'
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                              <span className="text-lg">🏦</span>
                            </div>
                            <div className="text-left">
                              <div className="font-semibold text-gray-900 dark:text-white">Payment Gateway</div>
                              <div className="text-sm text-gray-600 dark:text-gray-400">
                                QRIS, Bank Transfer, E-Wallet
                              </div>
                              <div className="text-sm font-medium text-blue-600 dark:text-blue-400">
                                Via Tripay
                              </div>
                            </div>
                          </div>
                        </button>
                      </div>

                      {/* Tripay Method Selection */}
                      {paymentMethod === 'TRIPAY' && (
                        <div className="space-y-4">
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Pilih Metode Pembayaran
                          </label>

                          {channelsLoading ? (
                            <div className="text-center py-4">
                              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">Memuat metode pembayaran...</p>
                            </div>
                          ) : (
                            <div className="space-y-4">
                              {Object.entries(groupChannelsByType(channels)).map(([group, groupChannels]) => (
                                <div key={group} className="space-y-2">
                                  <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">
                                    {group.replace('_', ' ')}
                                  </h5>
                                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                                    {groupChannels.map((channel) => (
                                      <button
                                        key={channel.code}
                                        onClick={() => setTripayMethod(channel.code)}
                                        className={`p-3 rounded-lg border transition-all duration-200 text-left ${
                                          tripayMethod === channel.code
                                            ? 'border-primary bg-primary/10'
                                            : 'border-gray-200 dark:border-gray-600 hover:border-primary/50'
                                        }`}
                                      >
                                        <div className="flex items-center space-x-3">
                                          <img
                                            src={channel.icon_url}
                                            alt={channel.name}
                                            className="w-8 h-8 object-contain"
                                          />
                                          <div className="flex-1 min-w-0">
                                            <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                              {channel.name}
                                            </div>
                                            <div className="text-xs text-gray-500 dark:text-gray-400">
                                              Fee: {formatFee(channel, selectedDuration?.price || 0)}
                                            </div>
                                          </div>
                                        </div>
                                      </button>
                                    ))}
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    <Button
                      onClick={() => handlePurchase('monthly')}
                      label={isPurchasing.monthly || isLoading ? 'Memproses...' : '🛒 Beli Sekarang'}
                      variant="primary"
                      shape="rounded"
                      className="w-full py-4 text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300"
                      disabled={
                        isPurchasing.monthly ||
                        isLoading ||
                        isWaitingPayment ||
                        !selectedDuration ||
                        !username ||
                        username.length < 6 ||
                        username.length > 12 ||
                        (paymentMethod === 'SALDO' && (balance || 0) < (selectedDuration?.price || 0))
                      }
                    />
                  </div>
                )}

                {activeTab === 'hourly' && (
                  <div className="space-y-6 animate-fadeIn">
                    <div className="text-center">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                        Paket Pay-as-You-Go
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        Bayar sesuai pemakaian, tanpa komitmen jangka panjang
                      </p>
                    </div>

                    <div className="bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-xl p-8 text-center border border-green-200 dark:border-green-700">
                      <div className="text-6xl mb-4">⚡</div>
                      <h4 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                        Rp {hourlyPrice.toLocaleString('id-ID')}/jam
                      </h4>
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        Sistem penagihan otomatis setiap jam
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div className="flex items-center justify-center space-x-2">
                          <span className="text-green-500">✓</span>
                          <span>Fleksibel</span>
                        </div>
                        <div className="flex items-center justify-center space-x-2">
                          <span className="text-green-500">✓</span>
                          <span>Tanpa Kontrak</span>
                        </div>
                        <div className="flex items-center justify-center space-x-2">
                          <span className="text-green-500">✓</span>
                          <span>Auto Billing</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <span className="text-yellow-500 text-xl">💡</span>
                        <div>
                          <h5 className="font-semibold text-yellow-800 dark:text-yellow-200">Cara Kerja:</h5>
                          <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                            Saldo akan dipotong otomatis setiap jam. Akun akan dinonaktifkan jika saldo habis.
                          </p>
                        </div>
                      </div>
                    </div>

                    <Button
                      onClick={() => handlePurchase('hourly')}
                      label={isPurchasing.hourly || isLoading ? 'Memproses...' : '⚡ Aktifkan Akun Per Jam'}
                      variant="green"
                      shape="rounded"
                      className="w-full py-4 text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300"
                      disabled={isPurchasing.hourly || isLoading || isWaitingPayment || !username || username.length < 6 || username.length > 12}
                    />
                  </div>
                )}
              </div>

              {/* Trial Button - Always Visible */}
              <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                <div className="text-center mb-4">
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    🎁 Ingin Mencoba Dulu?
                  </h4>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Dapatkan akses gratis selama 1 hari untuk menguji kualitas server
                  </p>
                </div>
                <Button
                  onClick={() => handlePurchase('trial')}
                  label={isPurchasing.trial || isLoading ? 'Memproses...' : '🆓 Coba Gratis (1 Hari)'}
                  variant="outlinePrimary"
                  shape="rounded"
                  className="w-full py-3 font-semibold border-2 hover:bg-primary hover:text-white transition-all duration-300"
                  disabled={isPurchasing.trial || isLoading || isWaitingPayment}
                />
              </div>
            </div>
          </ShowcaseSection>
        </div>
      </div>
    </>
  );
};

export default OrderDetailClient;
