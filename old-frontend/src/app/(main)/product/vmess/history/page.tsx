import { Metadata } from 'next';
import Breadcrumb from '@/components/Breadcrumbs/Breadcrumb';
import VmessHistoryTable from "@/components/product/vmess/VmessHistoryTable";
import { createAuthenticatedApiClient } from '@/lib/apiClientEnhanced';
import { HistoryApiResponse } from "@/types/history";

export const metadata: Metadata = {
  title: `History Layanan Vmess | ${process.env.NEXT_PUBLIC_APP_NAME}`,
  description: `Halaman history layanan Vmess untuk melihat semua layanan Vmess aktif dan riwayat pembelian Anda.`,
};

interface VmessHistoryPageProps {
  searchParams?: Promise<{
    page?: string | string[];
    limit?: string | string[];
    search?: string | string[];
  }>;
}

const fetchVmessHistoryData = async (searchParams?: {
  page?: string | string[];
  limit?: string | string[];
  search?: string | string[];
}): Promise<HistoryApiResponse> => {
    const page = Array.isArray(searchParams?.page) 
        ? searchParams.page[0] 
        : searchParams?.page || "1";
    const limit = Array.isArray(searchParams?.limit) 
        ? searchParams.limit[0] 
        : searchParams?.limit || "10";
    const search = Array.isArray(searchParams?.search) 
        ? searchParams.search[0] 
        : searchParams?.search || "";

    const params = new URLSearchParams();
    params.append('page', page);
    params.append('limit', limit);
    if (search) {
        params.append('search', search);
    }

    const apiClient = await createAuthenticatedApiClient();
    if (!apiClient) {
        console.error("Failed to create authenticated API client");
        return { history: [], page: Number(page), limit: Number(limit), total: 0, message: "Authentication failed" };
    }

    try {
        const data = await apiClient.get(`/accounts/history?${params.toString()}`);
        return data;
    } catch (error) {
        console.error("Error fetching vmess history data:", error);
        return { history: [], page: Number(page), limit: Number(limit), total: 0, message: "An error occurred" };
    }
};

const VmessHistoryPage = async ({ searchParams }: VmessHistoryPageProps) => {
  const resolvedSearchParams = await searchParams;
  const initialHistoryData = await fetchVmessHistoryData(resolvedSearchParams);

  return (
    <>
      <Breadcrumb pageName="History Layanan Vmess" />

      <div className="flex flex-col gap-10">
        <VmessHistoryTable initialData={initialHistoryData} />
      </div>
    </>
  );
};

export default VmessHistoryPage;
