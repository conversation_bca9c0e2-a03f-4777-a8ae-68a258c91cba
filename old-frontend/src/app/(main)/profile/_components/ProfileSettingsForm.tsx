"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useNotification } from "@/contexts/NotificationContext";
import type { UserProfile } from "@/types/user";
import StatusBadge from "./StatusBadge";

interface ProfileSettingsFormProps {
  user: UserProfile;
  accessToken: string;
}

export default function ProfileSettingsForm({ user, accessToken }: ProfileSettingsFormProps) {
  const router = useRouter();
  const { addNotification } = useNotification();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    email: user.email,
    whatsapp: user.whatsapp || "",
  });
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleCancel = () => {
    setIsEditing(false);
    setFormData({
      email: user.email,
      whatsapp: user.whatsapp || "",
    });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);

    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(formData.email)) {
      addNotification('Format email tidak valid. Silakan masukkan email yang valid.', 'error');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`/api/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Gagal memperbarui profil.');
      }

      addNotification('Profil berhasil diperbarui!', 'success');
      setIsEditing(false); // Exit edit mode on success
      router.refresh(); // Refresh server-side props to get updated profile data
    } catch (err: any) {
      addNotification(err.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h4 className="text-lg font-semibold text-dark dark:text-white">Informasi Pribadi</h4>
          <p className="text-sm text-dark-6">Kelola informasi dasar akun Anda</p>
        </div>
        {!isEditing && (
          <button
            type="button"
            onClick={() => setIsEditing(true)}
            className="inline-flex items-center gap-2 rounded-lg bg-primary px-4 py-2.5 text-sm font-medium text-white transition-all duration-300 hover:bg-primary/90 hover:scale-105"
          >
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Edit Profil
          </button>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Form Fields Grid */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Username (disabled) */}
          <div className="space-y-2">
            <label className="form-label">
              Nama Pengguna
            </label>
            <div className="relative">
              <input
                type="text"
                name="username"
                value={user.username}
                className="form-input disabled:cursor-not-allowed disabled:opacity-60"
                disabled
              />
              <div className="absolute right-3 top-1/2 -translate-y-1/2">
                <svg className="h-4 w-4 text-dark-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
            </div>
            <p className="text-xs text-dark-6">Nama pengguna tidak dapat diubah</p>
          </div>

          {/* Name */}
          <div className="space-y-2">
            <label className="form-label">
              Nama Lengkap
            </label>
            <div className="relative">
              <input
                type="text"
                name="name"
                value={user.name}
                className="form-input disabled:cursor-not-allowed disabled:opacity-60"
                disabled
              />
              <div className="absolute right-3 top-1/2 -translate-y-1/2">
                <svg className="h-4 w-4 text-dark-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Email */}
        <div className="space-y-2">
          <label className="form-label flex items-center gap-2">
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            Email
            <StatusBadge verified={user.emailVerified} />
          </label>
          <input
            type="email"
            name="email"
            placeholder="Masukkan alamat email Anda"
            value={formData.email}
            onChange={handleChange}
            className={`form-input ${
              !isEditing ? 'cursor-not-allowed opacity-60' : ''
            }`}
            disabled={!isEditing}
          />
          {user.emailVerifiedAt && (
            <p className="text-xs text-green">
              Diverifikasi pada {new Date(user.emailVerifiedAt).toLocaleDateString('id-ID', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </p>
          )}
        </div>

        {/* WhatsApp */}
        <div className="space-y-2">
          <label className="form-label flex items-center gap-2">
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            WhatsApp
            <StatusBadge verified={user.whatsappVerified} />
          </label>
          <input
            type="text"
            name="whatsapp"
            placeholder="Masukkan nomor WhatsApp Anda (contoh: +6281234567890)"
            value={formData.whatsapp}
            onChange={handleChange}
            className={`form-input ${
              !isEditing ? 'cursor-not-allowed opacity-60' : ''
            }`}
            disabled={!isEditing}
          />
          <p className="text-xs text-dark-6">Format: +62 diikuti nomor tanpa spasi atau tanda hubung</p>
        </div>

        {/* Action Buttons */}
        {isEditing && (
          <div className="flex justify-end gap-3 pt-4 border-t border-stroke dark:border-dark-3">
            <button
              type="button"
              onClick={handleCancel}
              className="form-button-secondary"
            >
              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              Batal
            </button>
            <button
              type="submit"
              disabled={loading}
              className="form-button-primary disabled:cursor-not-allowed disabled:opacity-50"
            >
              {loading ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                  Menyimpan...
                </>
              ) : (
                <>
                  <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Simpan Perubahan
                </>
              )}
            </button>
          </div>
        )}
      </form>
    </div>
  );
}
