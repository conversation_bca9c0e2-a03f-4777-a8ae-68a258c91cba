"use client";

import type { UserProfile } from "@/types/user";

interface UserStats {
  pay_bulanan: number;
  total_pay: number;
  batas_trial: number;
  trial: number;
  jumlah_akun_trial: number;
  jumlah_akun_hourly: number;
  jumlah_akun_month: number;
  jumlah_akun_trojan: number;
  jumlah_akun_vmess: number;
  jumlah_akun_vless: number;
  jumlah_akun_ssh: number;
  total_account: number;
}

interface ProfileStatsProps {
  user: UserProfile;
  userStats: UserStats;
}

export default function ProfileStats({ user, userStats }: ProfileStatsProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark">
      {/* Header */}
      <div className="mb-6 border-b border-stroke pb-4 dark:border-dark-3">
        <h3 className="text-lg font-semibold text-dark dark:text-white">Statistik Akun</h3>
        <p className="text-sm text-dark-6">Ringkasan aktivitas dan penggunaan layanan Anda</p>
      </div>

      {/* Stats List */}
      <div className="space-y-6">
        {/* Financial Section */}
        <div>
          <h4 className="mb-3 flex items-center gap-2 text-sm font-medium text-dark-6">
            <div className="h-1 w-1 rounded-full bg-primary"></div>
            Keuangan
          </h4>
          <div className="space-y-3">
            <div className="flex items-center justify-between rounded-lg bg-gray-1 p-3 dark:bg-dark-2">
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green/10">
                  <svg className="h-4 w-4 text-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <span className="text-sm font-medium text-dark dark:text-white">Pengeluaran Bulan Ini</span>
              </div>
              <span className="text-sm font-semibold text-dark dark:text-white">{formatCurrency(userStats.pay_bulanan || 0)}</span>
            </div>
            
            <div className="flex items-center justify-between rounded-lg bg-gray-1 p-3 dark:bg-dark-2">
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue/10">
                  <svg className="h-4 w-4 text-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <span className="text-sm font-medium text-dark dark:text-white">Total Pengeluaran</span>
              </div>
              <span className="text-sm font-semibold text-dark dark:text-white">{formatCurrency(userStats.total_pay || 0)}</span>
            </div>
            
            <div className="flex items-center justify-between rounded-lg bg-gray-1 p-3 dark:bg-dark-2">
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-orange/10">
                  <svg className="h-4 w-4 text-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <span className="text-sm font-medium text-dark dark:text-white">Status Trial</span>
              </div>
              <span className="text-sm font-semibold text-dark dark:text-white">{userStats.trial || 0} / {userStats.batas_trial || 5}</span>
            </div>
          </div>
        </div>

        {/* Accounts Section */}
        <div>
          <h4 className="mb-3 flex items-center gap-2 text-sm font-medium text-dark-6">
            <div className="h-1 w-1 rounded-full bg-primary"></div>
            Akun Aktif
          </h4>
          <div className="grid gap-3 sm:grid-cols-2">
            <div className="flex items-center justify-between rounded-lg bg-gray-1 p-3 dark:bg-dark-2">
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-purple/10">
                  <svg className="h-4 w-4 text-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <span className="text-sm font-medium text-dark dark:text-white">Total Akun</span>
              </div>
              <span className="text-sm font-semibold text-dark dark:text-white">{userStats.total_account || 0}</span>
            </div>
            
            <div className="flex items-center justify-between rounded-lg bg-gray-1 p-3 dark:bg-dark-2">
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-indigo/10">
                  <svg className="h-4 w-4 text-indigo" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <span className="text-sm font-medium text-dark dark:text-white">Akun Per Jam</span>
              </div>
              <span className="text-sm font-semibold text-dark dark:text-white">{userStats.jumlah_akun_hourly || 0}</span>
            </div>
            
            <div className="flex items-center justify-between rounded-lg bg-gray-1 p-3 dark:bg-dark-2">
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue/10">
                  <svg className="h-4 w-4 text-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <span className="text-sm font-medium text-dark dark:text-white">Akun Bulanan</span>
              </div>
              <span className="text-sm font-semibold text-dark dark:text-white">{userStats.jumlah_akun_month || 0}</span>
            </div>
            
            <div className="flex items-center justify-between rounded-lg bg-gray-1 p-3 dark:bg-dark-2">
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-orange/10">
                  <svg className="h-4 w-4 text-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <span className="text-sm font-medium text-dark dark:text-white">Akun Trial</span>
              </div>
              <span className="text-sm font-semibold text-dark dark:text-white">{userStats.jumlah_akun_trial || 0}</span>
            </div>
          </div>
        </div>

        {/* VPN Protocols Section */}
        <div>
          <h4 className="mb-3 flex items-center gap-2 text-sm font-medium text-dark-6">
            <div className="h-1 w-1 rounded-full bg-primary"></div>
            Protokol VPN
          </h4>
          <div className="grid gap-3 sm:grid-cols-2">
            <div className="flex items-center justify-between rounded-lg bg-gray-1 p-3 dark:bg-dark-2">
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-pink/10">
                  <svg className="h-4 w-4 text-pink" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <span className="text-sm font-medium text-dark dark:text-white">Trojan</span>
              </div>
              <span className="text-sm font-semibold text-dark dark:text-white">{userStats.jumlah_akun_trojan || 0}</span>
            </div>
            
            <div className="flex items-center justify-between rounded-lg bg-gray-1 p-3 dark:bg-dark-2">
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-pink/10">
                  <svg className="h-4 w-4 text-pink" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <span className="text-sm font-medium text-dark dark:text-white">VMess</span>
              </div>
              <span className="text-sm font-semibold text-dark dark:text-white">{userStats.jumlah_akun_vmess || 0}</span>
            </div>
            
            <div className="flex items-center justify-between rounded-lg bg-gray-1 p-3 dark:bg-dark-2">
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-pink/10">
                  <svg className="h-4 w-4 text-pink" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <span className="text-sm font-medium text-dark dark:text-white">VLess</span>
              </div>
              <span className="text-sm font-semibold text-dark dark:text-white">{userStats.jumlah_akun_vless || 0}</span>
            </div>
            
            <div className="flex items-center justify-between rounded-lg bg-gray-1 p-3 dark:bg-dark-2">
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-pink/10">
                  <svg className="h-4 w-4 text-pink" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <span className="text-sm font-medium text-dark dark:text-white">SSH</span>
              </div>
              <span className="text-sm font-semibold text-dark dark:text-white">{userStats.jumlah_akun_ssh || 0}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
