import React from 'react';

interface StatusBadgeProps {
  verified: boolean;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ verified }) => {
  const badgeClasses = verified
    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';

  const text = verified ? 'Terverifikasi' : 'Belum Terverifikasi';

  return (
    <span
      className={`ml-2 rounded px-2.5 py-0.5 text-xs font-medium ${badgeClasses}`}>
      {text}
    </span>
  );
};

export default StatusBadge;
