import { Metadata } from 'next';
import Breadcrumb from '@/components/Breadcrumbs/Breadcrumb';
import TransactionTable from "@/components/Tables/TransactionTable";
import { createAuthenticatedApiClient } from '@/lib/apiClientEnhanced';
import { TransactionApiResponse } from "@/types/transaction";

export const metadata: Metadata = {
  title: `Riwayat Transaksi | ${process.env.NEXT_PUBLIC_APP_NAME}`,
  description: `Halaman riwayat transaksi untuk melihat semua transaksi dan pembayaran Anda.`,
};

interface TransactionPageProps {
  searchParams?: Promise<{
    page?: string | string[];
    limit?: string | string[];
    search?: string | string[];
    type?: string | string[];
    status?: string | string[];
  }>;
}

const fetchTransactionData = async (searchParams?: {
  page?: string | string[];
  limit?: string | string[];
  search?: string | string[];
  type?: string | string[];
  status?: string | string[];
}): Promise<TransactionApiResponse> => {
    const page = Array.isArray(searchParams?.page) 
        ? searchParams.page[0] 
        : searchParams?.page || "1";
    const limit = Array.isArray(searchParams?.limit) 
        ? searchParams.limit[0] 
        : searchParams?.limit || "10";
    const search = Array.isArray(searchParams?.search) 
        ? searchParams.search[0] 
        : searchParams?.search || "";
    const type = Array.isArray(searchParams?.type) 
        ? searchParams.type[0] 
        : searchParams?.type || "";
    const status = Array.isArray(searchParams?.status) 
        ? searchParams.status[0] 
        : searchParams?.status || "";

    const params = new URLSearchParams();
    params.append('page', page);
    params.append('limit', limit);
    if (search) {
        params.append('search', search);
    }
    if (type) {
        params.append('type', type);
    }
    if (status) {
        params.append('status', status);
    }

    const apiClient = await createAuthenticatedApiClient();
    if (!apiClient) {
        console.error("Failed to create authenticated API client");
        return {
            data: [],
            pagination: {
                total: 0,
                per_page: Number(limit),
                current_page: Number(page),
                last_page: 0
            },
            message: "Authentication failed"
        };
    }

    try {
        const data = await apiClient.get(`/users/me/transactions?${params.toString()}`);
        return data;
    } catch (error) {
        console.error("Error fetching transaction data:", error);
        return {
            data: [],
            pagination: {
                total: 0,
                per_page: Number(limit),
                current_page: Number(page),
                last_page: 0
            },
            message: "An error occurred"
        };
    }
};

const TransactionPage = async ({ searchParams }: TransactionPageProps) => {
  const resolvedSearchParams = await searchParams;
  const initialTransactionData = await fetchTransactionData(resolvedSearchParams);

  return (
    <>
      <Breadcrumb pageName="Riwayat Transaksi" />

      <div className="flex flex-col gap-10">
        <TransactionTable initialData={initialTransactionData} />
      </div>
    </>
  );
};

export default TransactionPage;
