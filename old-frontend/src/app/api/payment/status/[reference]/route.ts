import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createApiClientFromRequest } from '@/lib/apiClientEnhanced';

export async function GET(req: NextRequest, { params }: { params: Promise<{ reference: string }> }) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  try {
    const resolvedParams = await params;
    const orderId = resolvedParams.reference;

    if (!orderId) {
      return NextResponse.json({
        error: 'Order ID diperlukan'
      }, { status: 400 });
    }

    const apiClient = await createApiClientFromRequest(req);

    // Gunakan endpoint status Tripay yang baru dibuat
    let backendData;
    try {
      console.log(`🔍 Checking payment status for order: ${orderId}`);
      backendData = await apiClient.get(`/payment/status/${orderId}`);
      console.log(`✅ Payment status retrieved:`, backendData);
    } catch (error: any) {
      console.log(`❌ Failed to get payment status:`, error.response?.status);

      // Jika transaksi tidak ditemukan, return status PENDING
      if (error.response?.status === 404) {
        return NextResponse.json({
          status: 'PENDING',
          message: 'Transaction not found, payment might still be processing'
        }, { status: 200 });
      }

      throw error;
    }

    console.log('Payment Status Data:', JSON.stringify(backendData, null, 2));

    // Pastikan response dalam format yang benar untuk frontend
    return NextResponse.json(backendData, { status: 200 });

  } catch (error: any) {
    console.error('Error in payment status API route:', error);
    
    // Jika error dari backend API, teruskan response asli
    if (error.response && error.response.data) {
      return NextResponse.json(error.response.data, { status: error.response.status || 500 });
    }
    
    // Fallback untuk error lainnya
    return NextResponse.json({ 
      error: 'Terjadi kesalahan saat mengecek status pembayaran',
      message: 'Internal server error' 
    }, { status: 500 });
  }
}
