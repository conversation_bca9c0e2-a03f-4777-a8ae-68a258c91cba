import { getToken } from "next-auth/jwt";
import { NextRequest, NextResponse } from "next/server";
import { createApiClientFromRequest } from "@/lib/apiClientEnhanced";

async function getAccessToken(req: NextRequest): Promise<string | null> {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  return token?.accessToken as string | null;
}

// GET - Test server token
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const accessToken = await getAccessToken(req);

    if (!accessToken) {
      return NextResponse.json(
        { message: "Tidak terautentikasi" },
        { status: 401 }
      );
    }

    const serverId = params.id;
    
    const apiClient = await createApiClientFromRequest(req);
    const data = await apiClient.get(`/servers/${serverId}/test-token`);
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error testing server token:", error);
    
    // Jika error memiliki respons dari API backend, teruskan respons tersebut
    if (error && typeof error === 'object' && 'response' in error && error.response && typeof error.response === 'object' && 'data' in error.response && 'status' in error.response) {
      const status = typeof error.response.status === 'number' ? error.response.status : 500;
      return NextResponse.json(
        error.response.data,
        { status: status }
      );
    }
    
    return NextResponse.json(
      { message: "Terjadi kesalahan server" },
      { status: 500 }
    );
  }
}