import { getToken } from "next-auth/jwt";
import { NextRequest, NextResponse } from "next/server";
import { createApiClientFromRequest } from "@/lib/apiClientEnhanced";

async function getAccessToken(req: NextRequest): Promise<string | null> {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  return token?.accessToken as string | null;
}

// GET - Fetch all servers
export async function GET(req: NextRequest) {
  try {
    const accessToken = await getAccessToken(req);

    if (!accessToken) {
      return NextResponse.json(
        { message: "Tidak terautentikasi" },
        { status: 401 }
      );
    }

    const apiClient = await createApiClientFromRequest(req);
    const data = await apiClient.get('/servers');
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error fetching servers:", error);
    return NextResponse.json(
      { message: "<PERSON><PERSON><PERSON><PERSON> k<PERSON> server" },
      { status: 500 }
    );
  }
}

// POST - Create a new server
export async function POST(req: NextRequest) {
  try {
    const accessToken = await getAccessToken(req);

    if (!accessToken) {
      return NextResponse.json(
        { message: "Tidak terautentikasi" },
        { status: 401 }
      );
    }

    const data = await req.json();

    const apiClient = await createApiClientFromRequest(req);
    const responseData = await apiClient.post('/servers', data);
    return NextResponse.json(responseData, { status: 201 });
  } catch (error) {
    console.error("Error creating server:", error);
    return NextResponse.json(
      { message: "Terjadi kesalahan server" },
      { status: 500 }
    );
  }
}