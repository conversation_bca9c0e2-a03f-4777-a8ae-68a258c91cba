import { createApiClientFromRequest } from '@/lib/apiClientEnhanced';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const type = request.nextUrl.searchParams.get('type');
    if (!type || !['topup', 'purchase', 'trial'].includes(type)) {
      return NextResponse.json({ error: 'Invalid transaction type' }, { status: 400 });
    }

    const client = await createApiClientFromRequest(request);
    const data = await client.get(`/public/transactions?type=${type}`);

    if (!Array.isArray(data)) {
      console.error('Unexpected response format:', data);
      return NextResponse.json(
        { error: 'Unexpected response format from server' },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error fetching transactions:', errorMessage);
    
    if (errorMessage.includes('Tidak terautentikasi')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Log error lengkap untuk debugging
    console.error('Full error object:', error);

    return NextResponse.json(
      { error: 'Failed to fetch transactions', details: errorMessage },
      { status: 500 }
    );
  }
}