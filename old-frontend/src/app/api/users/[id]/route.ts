import { getToken } from 'next-auth/jwt';
import { createApiClientFromRequest } from '@/lib/apiClientEnhanced';
import { NextRequest, NextResponse } from 'next/server';
import { authOptions } from '@/lib/auth';

export async function PUT(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const session = await getToken({ req });

  if (!session || !session.accessToken) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await req.json();
    const apiClient = await createApiClientFromRequest(req);
    const data = await apiClient.put(`/admin/users/${id}`, body);
    return NextResponse.json(data, { status: 200 });

  } catch (error) {
    return NextResponse.json({ message: 'An unexpected error occurred' }, { status: 500 });
  }
}
