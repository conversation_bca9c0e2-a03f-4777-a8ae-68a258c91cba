"use client";

import "@/css/satoshi.css";
import "@/css/badge.css";
import "@/css/style.css";
import "@/css/form.css";
import "@/css/notification.css";
import "@/css/card.css";
import "@/css/tabs.css";

import "flatpickr/dist/flatpickr.min.css";
import "jsvectormap/dist/jsvectormap.css";

import NextTopLoader from "nextjs-toploader";
import type { PropsWithChildren } from "react";
import { SessionProvider } from "next-auth/react";
import { Providers } from "./providers";
import { NotificationProvider } from '@/contexts/NotificationContext';

export default function RootLayout({ children }: PropsWithChildren) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <SessionProvider>
          <Providers>
            <NotificationProvider>
              <NextTopLoader color="#5750F1" showSpinner={false} />
              {children}
            </NotificationProvider>
          </Providers>
        </SessionProvider>
      </body>
    </html>
  );
}
