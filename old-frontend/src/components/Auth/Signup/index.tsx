"use client";

import InputGroup from "../../FormElements/InputGroup";
import { EmailIcon, PasswordIcon, GlobeIcon, UserIcon } from "@/assets/icons";
import { ApiClient } from "@/lib/apiClientEnhanced";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { signIn } from "next-auth/react";
import { useNotification } from "@/contexts/NotificationContext";

export default function Signup() {
    const router = useRouter();
  const { addNotification } = useNotification();
  const [data, setData] = useState({
    name: "",
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    whatsapp: "",
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setData({
      ...data,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (data.password !== data.confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const apiClient = new ApiClient();
      await apiClient.post('/auth/register', {
        name: data.name,
        username: data.username,
        email: data.email,
        password: data.password,
        whatsapp: data.whatsapp
      });

      // Pendaftaran berhasil, sekarang coba untuk login secara otomatis
      const result = await signIn("credentials", {
        redirect: false,
        identifier: data.username,
        password: data.password,
      });

      if (result?.ok) {
        addNotification("Pendaftaran berhasil!", "success");
        router.push("/dashboard");
      } else {
        // Jika auto-login gagal, beri tahu pengguna dan arahkan ke halaman login manual
        addNotification("Pendaftaran berhasil, silakan masuk.", "success");
        router.push("/");
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <h2 className="text-2xl font-bold text-center text-dark dark:text-white">Create an Account</h2>

      <InputGroup
        label="Full Name"
        name="name"
        placeholder="Enter your full name"
        type="text"
        value={data.name}
        handleChange={handleChange}
        icon={<UserIcon />}
      />

      <InputGroup
        label="Username"
        name="username"
        placeholder="Enter your username"
        type="text"
        value={data.username}
        handleChange={handleChange}
        icon={<UserIcon />}
      />

      <InputGroup
        label="Email"
        name="email"
        placeholder="Enter your email"
        type="email"
        value={data.email}
        handleChange={handleChange}
        icon={<EmailIcon />}
      />

      <InputGroup
        label="Password"
        name="password"
        placeholder="Enter your password"
        type="password"
        value={data.password}
        handleChange={handleChange}
        icon={<PasswordIcon />}
      />

      <InputGroup
        label="Confirm Password"
        name="confirmPassword"
        placeholder="Confirm your password"
        type="password"
        value={data.confirmPassword}
        handleChange={handleChange}
        icon={<PasswordIcon />}
      />

      <InputGroup
        label="WhatsApp Number"
        name="whatsapp"
        placeholder="Enter your WhatsApp number"
        type="text"
        value={data.whatsapp}
        handleChange={handleChange}
        icon={<GlobeIcon />}
      />

      {error && (
        <div className="rounded-md bg-red-100 p-3 text-center text-sm text-red-700 dark:bg-red-900/30 dark:text-red-300">
          {error}
        </div>
      )}

      <button
        type="submit"
        disabled={loading}
        className="flex w-full cursor-pointer items-center justify-center gap-2 rounded-lg bg-primary p-4 font-medium text-white transition hover:bg-opacity-90 disabled:cursor-not-allowed disabled:opacity-50"
      >
        Create Account
        {loading && (
          <span className="inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-white border-t-transparent" />
        )}
      </button>

      <div className="text-center font-medium text-dark dark:text-white">
        <span>Already have an account? </span>
        <Link href="/" className="text-primary hover:underline">
          Sign In
        </Link>
      </div>
    </form>
  );
}
