"use client";

import { useBalance } from '@/contexts/BalanceContext';
import { WalletIcon } from "./icons";

const UserBalance = () => {
  const { balance, isLoading } = useBalance();

  return (
    <div className="flex items-center gap-2 rounded-full bg-gray-2 px-4 py-2 dark:bg-dark-2">
      <WalletIcon className="h-6 w-6 text-primary" />
      <span className="font-bold text-dark dark:text-white">
        {isLoading
          ? "Memuat..."
          : balance !== null
          ? new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0, maximumFractionDigits: 0 }).format(balance)
          : "Rp 0"}
      </span>
    </div>
  );
};

// Compact version for mobile/dropdown
export const CompactUserBalance = () => {
  const { balance, isLoading } = useBalance();

  return (
    <div className="flex items-center gap-3 px-4 py-3 border-b border-stroke dark:border-dark-3">
      <WalletIcon className="h-5 w-5 text-primary" />
      <div className="flex-1">
        <p className="text-xs text-body-color dark:text-dark-6">Saldo Anda</p>
        <p className="font-semibold text-dark dark:text-white">
          {isLoading
            ? "Memuat..."
            : balance !== null
            ? new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0, maximumFractionDigits: 0 }).format(balance)
            : "Rp 0"}
        </p>
      </div>
    </div>
  );
};

export default UserBalance;
