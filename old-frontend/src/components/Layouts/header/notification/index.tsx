"use client";

import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { useIsMobile } from "@/hooks/use-mobile";
import { useNotifications } from "@/hooks/useNotifications";
import { cn } from "@/lib/utils";
import { formatDistanceToNow } from "date-fns";
import { id } from "date-fns/locale";
import { useState, useEffect } from "react";
import { BellIcon } from "./icons";

// Notification type icons
const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'trial':
      return '🆓';
    case 'monthly':
      return '📅';
    case 'hourly':
      return '⏰';
    case 'renewal':
      return '🔄';
    case 'topup':
      return '💰';
    case 'payment':
      return '💳';
    case 'billing':
      return '📊';
    default:
      return '🔔';
  }
};

export function NotificationNew() {
  const [isOpen, setIsOpen] = useState(false);
  const isMobile = useIsMobile();
  const {
    notifications,
    stats,
    loading,
    error,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    hasUnread,
    unreadCount
  } = useNotifications();

  // Fetch notifications when dropdown opens
  useEffect(() => {
    if (isOpen && (!notifications || notifications.length === 0)) {
      fetchNotifications(1, 10);
    }
  }, [isOpen, notifications?.length, fetchNotifications]);

  const handleNotificationClick = async (notification: any) => {
    if (!notification.is_read) {
      try {
        await markAsRead([notification.id]);
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    }
    setIsOpen(false);
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  return (
    <Dropdown
      isOpen={isOpen}
      setIsOpen={setIsOpen}
    >
      <DropdownTrigger
        className="grid size-12 place-items-center rounded-full border bg-gray-2 text-dark outline-none hover:text-primary focus-visible:border-primary focus-visible:text-primary dark:border-dark-4 dark:bg-dark-3 dark:text-white dark:focus-visible:border-primary"
        aria-label="View Notifications"
      >
        <span className="relative">
          <BellIcon />
          {hasUnread && (
            <span className="absolute -top-1 -right-1 z-1 flex size-5 items-center justify-center rounded-full bg-red text-xs text-white font-medium">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
        </span>
      </DropdownTrigger>

      <DropdownContent
        align={isMobile ? "end" : "center"}
        className="border border-stroke bg-white px-0 py-0 shadow-md dark:border-dark-3 dark:bg-gray-dark min-[350px]:min-w-[20rem] max-w-sm"
      >
        <div className="flex items-center justify-between px-4 py-3 border-b border-stroke dark:border-dark-3">
          <span className="text-lg font-medium text-dark dark:text-white">
            Notifikasi {unreadCount > 0 && `(${unreadCount})`}
          </span>
          {unreadCount > 0 && (
            <button
              onClick={handleMarkAllAsRead}
              className="text-xs text-primary hover:underline"
            >
              Tandai semua dibaca
            </button>
          )}
        </div>

        <div className="max-h-[23rem] overflow-y-auto">
          {loading && (!notifications || notifications.length === 0) ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-sm text-body-color dark:text-dark-6">
                Memuat notifikasi...
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-sm text-red-500">
                {error}
              </div>
            </div>
          ) : !notifications || notifications.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-sm text-body-color dark:text-dark-6">
                Tidak ada notifikasi
              </div>
            </div>
          ) : (
            <ul className="space-y-0">
              {notifications.map((notification) => (
                <li key={notification.id} role="menuitem">
                  <button
                    onClick={() => handleNotificationClick(notification)}
                    className={cn(
                      "w-full flex items-start gap-3 px-4 py-3 text-left outline-none hover:bg-gray-2 focus-visible:bg-gray-2 dark:hover:bg-dark-3 dark:focus-visible:bg-dark-3 border-b border-stroke dark:border-dark-3 last:border-b-0",
                      !notification.is_read && "bg-blue-50 dark:bg-blue-900/20"
                    )}
                  >
                    <div className="flex-shrink-0 text-lg mt-0.5">
                      {getNotificationIcon(notification.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <h6 className={cn(
                          "text-sm font-medium text-dark dark:text-white",
                          !notification.is_read && "font-semibold"
                        )}>
                          {notification.title}
                        </h6>
                        {!notification.is_read && (
                          <div className="flex-shrink-0 size-2 rounded-full bg-primary ml-2 mt-1"></div>
                        )}
                      </div>
                      
                      <p className="text-xs text-body-color dark:text-dark-6 mt-1 line-clamp-2">
                        {notification.message}
                      </p>
                      
                      <span className="text-xs text-body-color dark:text-dark-6 mt-1">
                        {formatDistanceToNow(new Date(notification.created_at), { 
                          addSuffix: true, 
                          locale: id 
                        })}
                      </span>
                    </div>
                  </button>
                </li>
              ))}
            </ul>
          )}
        </div>

        {notifications && notifications.length > 0 && (
          <div className="border-t border-stroke dark:border-dark-3">
            <a
              href="/notifications"
              onClick={() => setIsOpen(false)}
              className="block w-full py-3 text-center text-sm font-medium text-primary hover:bg-gray-2 dark:hover:bg-dark-3"
            >
              Lihat semua notifikasi
            </a>
          </div>
        )}
      </DropdownContent>
    </Dropdown>
  );
}
