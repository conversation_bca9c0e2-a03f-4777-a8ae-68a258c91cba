"use client";

import React, { ReactNode } from 'react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity">
      <div className="relative w-full max-w-2xl rounded-lg bg-white p-6 shadow-lg dark:bg-gray-dark">
        <div className="flex items-center justify-between border-b border-stroke pb-4 dark:border-dark-3">
          <h3 className="font-semibold text-black dark:text-white">{title}</h3>
          <button onClick={onClose} className="text-black dark:text-white">
            <svg
              className="size-5 fill-current"
              role="button"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M10 9.293l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414L10 8.586z"
                clipRule="evenodd"
              ></path>
            </svg>
          </button>
        </div>
        <div className="py-4">{children}</div>
      </div>
    </div>
  );
};

export default Modal;
