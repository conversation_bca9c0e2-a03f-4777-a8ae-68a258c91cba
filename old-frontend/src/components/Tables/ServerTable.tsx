"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import dayjs from "dayjs";

import { EyeIcon, EditIcon } from "../Layouts/sidebar/icons";

// Define TrashIcon component
const TrashIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={18}
    height={18}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M3 6h18" />
    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
  </svg>
);

const EyeOffIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
    <line x1="1" y1="1" x2="23" y2="23" />
  </svg>
);

const TestTubeIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={18}
    height={18}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2" />
    <line x1="8" y1="2" x2="16" y2="2" />
    <line x1="8" y1="8" x2="12" y2="8" />
  </svg>
);

import React, { useState, useMemo } from "react";
import { useRouter } from 'next/navigation';
import { useSession } from "next-auth/react";
import Modal from "../Modals/Modal"; // Import the modal component
import { useNotification } from "@/contexts/NotificationContext";
import { ApiClient } from "@/lib/apiClientEnhanced";

// Define a type for the server prop for better type safety
type Server = {
  id?: number;
  server_id: number;
  nama: string;
  kode: string;
  domain: string;
  token: string;
  negara: string;
  nama_isp: string;
  harga_member: number;
  harga_reseller: number;
  ssh: string;
  trojan: string;
  vmess: string;
  vless: string;
  slot_server: number;
  slot_terpakai: number;
  total_user: number;
  max_device: number;
  created_at: string;
  updated_at: string;
};

// Form data types
type ServerFormData = {
  nama: string;
  kode: string;
  domain: string;
  token: string;
  negara: string;
  nama_isp: string;
  harga_member: number;
  harga_reseller: number;
  ssh: string;
  trojan: string;
  vmess: string;
  vless: string;
  slot_server: number;
  slot_terpakai: number;
  total_user: number;
  max_device: number;
};

const ITEMS_PER_PAGE = 10;

const ServerTable = ({ servers = [] }: { servers: Server[] }) => {
  const { data: session } = useSession();
  const apiClient = new ApiClient();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedServer, setSelectedServer] = useState<Server | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isTokenVisible, setIsTokenVisible] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const [formData, setFormData] = useState<ServerFormData>({
    nama: "",
    kode: "",
    domain: "",
    token: "",
    negara: "",
    nama_isp: "",
    harga_member: 0,
    harga_reseller: 0,
    ssh: "",
    trojan: "",
    vmess: "",
    vless: "",
    slot_server: 0,
    slot_terpakai: 0,
    total_user: 0,
    max_device: 1
  });
  
  const { addNotification } = useNotification();
  const router = useRouter();

  // Initialize empty form for add modal
  const initializeAddForm = () => {
    setFormData({
      nama: "",
      kode: "",
      domain: "",
      token: "",
      negara: "Indonesia",
      nama_isp: "",
      harga_member: 0,
      harga_reseller: 0,
      ssh: "disabled",
      trojan: "disabled",
      vmess: "disabled",
      vless: "disabled",
      slot_server: 0,
      slot_terpakai: 0,
      total_user: 0,
      max_device: 1
    });
    setIsAddModalOpen(true);
  };

  // Handle view server details
  const handleViewServer = (server: Server) => {
    setSelectedServer(server);
    setIsViewModalOpen(true);
    setIsTokenVisible(false); // Reset token visibility when opening modal
  };

  // Handle edit server
  const handleEditServer = (server: Server) => {
    setSelectedServer(server);
    setFormData({
      nama: server.nama,
      kode: server.kode,
      domain: server.domain,
      token: server.token,
      negara: server.negara,
      nama_isp: server.nama_isp,
      harga_member: server.harga_member,
      harga_reseller: server.harga_reseller,
      ssh: server.ssh,
      trojan: server.trojan,
      vmess: server.vmess,
      vless: server.vless,
      slot_server: server.slot_server,
      slot_terpakai: server.slot_terpakai,
      total_user: server.total_user,
      max_device: server.max_device
    });
    setIsEditModalOpen(true);
  };

  // Handle delete server
  const handleDeleteServer = (server: Server) => {
    setSelectedServer(server);
    setIsDeleteModalOpen(true);
  };

  // Handle form input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    let processedValue: string | number = value;
    
    // Convert to number for numeric fields
    if (['harga_member', 'harga_reseller', 'slot_server', 'slot_terpakai', 'total_user', 'max_device'].includes(name)) {
      processedValue = value === '' ? 0 : Number(value);
    }
    
    setFormData(prev => ({ ...prev, [name]: processedValue }));
  };

  // Submit add server form
  const handleAddSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      const res = await fetch(`/api/servers`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await res.json();

      if (!res.ok) {
        throw new Error(data.message || 'Gagal menambahkan server.');
      }

      addNotification('Server berhasil ditambahkan!', 'success');
      setIsAddModalOpen(false);
      router.refresh();

    } catch (error: any) {
      addNotification(error.message, 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Submit edit server form
  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedServer) return;
    
    setIsSubmitting(true);
    
    try {
      const res = await fetch(`/api/servers/${selectedServer.server_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await res.json();

      if (!res.ok) {
        throw new Error(data.message || 'Gagal memperbarui server.');
      }

      addNotification('Server berhasil diperbarui!', 'success');
      setIsEditModalOpen(false);
      router.refresh();

    } catch (error: any) {
      addNotification(error.message, 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Submit delete server
  const handleDeleteSubmit = async () => {
    if (!selectedServer) return;
    
    setIsSubmitting(true);
    
    try {
      const res = await fetch(`/api/servers/${selectedServer.server_id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!res.ok) {
        const data = await res.json();
        throw new Error(data.message || 'Gagal menghapus server.');
      }

      addNotification('Server berhasil dihapus!', 'success');
      setIsDeleteModalOpen(false);
      router.refresh();

    } catch (error: any) {
      addNotification(error.message, 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

    const handleTestToken = async (serverId: number) => {

    try {
      const response = await fetch(`/api/servers/${serverId}/test-token`);
      const contentType = response.headers.get("content-type");

      if (response.ok) {
        const data = await response.json();
        addNotification(data.message, 'success');
      } else {
        let errorMessage;
        if (contentType && contentType.includes("application/json")) {
          const errorData = await response.json();
          errorMessage = errorData.error || errorData.message;
        } else {
          errorMessage = await response.text();
        }
        throw new Error(errorMessage || `Gagal menguji token. Status: ${response.status}`);
      }
    } catch (error: any) {
      addNotification(`Tes Gagal: ${error.message}`, 'error');
    }
  };

  const handleTestNewToken = async () => {
    const { domain, token } = formData;
    if (!domain || !token) {
      addNotification('Domain dan Token harus diisi untuk pengujian.', 'warning');
      return;
    }

    setIsSubmitting(true);
    try {
      if (!session?.accessToken) {
        addNotification('Sesi tidak valid atau Anda tidak terautentikasi.', 'error');
        return;
      }

      const apiClient = new ApiClient();
      const requestData = {
        domain: domain,
        token: token
      };

      const data = await apiClient.post('/servers/test-token', requestData, { token: session.accessToken });
      addNotification(data.message, 'success');

    } catch (error: any) {
      console.error('Error saat menguji token:', error);
      addNotification(error.message || 'Terjadi kesalahan jaringan.', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Filter servers based on search query
  const filteredServers = useMemo(() => {
    if (!servers) return [];
    
    const sortedServers = [...servers].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

    return sortedServers.filter((server) => {
      if (!searchQuery) return true; // Keep server if no search query
      
      // Filter by search query
      return (
        (server.nama && server.nama.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (server.kode && server.kode.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (server.domain && server.domain.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (server.negara && server.negara.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    });
  }, [servers, searchQuery]);

  // Paginate servers
  const paginatedServers = useMemo(() => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    return filteredServers.slice(startIndex, endIndex);
  }, [filteredServers, currentPage]);

  const totalPages = Math.ceil(filteredServers.length / ITEMS_PER_PAGE);

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
    setCurrentPage(1); // Reset to the first page on a new search
  };

  // Pagination controls
  const goToPage = (page: number) => {
    setCurrentPage(page);
  };

  const goToNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const goToPreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  // Function to generate pagination links
  const getPaginationLinks = () => {
    const elements = [];
    
    // Previous button
    elements.push(
      <li key="prev">
        <button
          className="flex size-8 items-center justify-center rounded-[3px] hover:bg-primary hover:text-white disabled:pointer-events-none disabled:opacity-50"
          onClick={goToPreviousPage}
          disabled={currentPage === 1}
        >
          <span className="sr-only">Go to previous page</span>
          <svg width="18" height="18" viewBox="0 0 18 18" fill="currentColor"><path d="M12.1758 16.1158C12.007 16.1158 11.8383 16.0596 11.7258 15.9189L5.36953 9.45019C5.11641 9.19707 5.11641 8.80332 5.36953 8.5502L11.7258 2.08145C11.9789 1.82832 12.3727 1.82832 12.6258 2.08145C12.8789 2.33457 12.8789 2.72832 12.6258 2.98145L6.71953 9.0002L12.6539 15.0189C12.907 15.2721 12.907 15.6658 12.6539 15.9189C12.4852 16.0314 12.3445 16.1158 12.1758 16.1158Z"></path></svg>
        </button>
      </li>
    );
    
    // Generate page numbers
    for (let i = 1; i <= totalPages; i++) {
        if (totalPages <= 5 || (i >= currentPage - 2 && i <= currentPage + 2) || i === 1 || i === totalPages) {
             elements.push(
                <li key={`page-${i}`}>
                    <button
                    className={cn("flex items-center justify-center rounded-[3px] px-3 py-1.5 font-medium hover:bg-primary hover:text-white", { 'bg-primary text-white': currentPage === i })}
                    onClick={() => goToPage(i)}
                    >
                    {i}
                    </button>
                </li>
            );
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            elements.push(<li key={`ellipsis-${i}`}><span className="flex items-center justify-center px-3 py-1.5">...</span></li>);
        }
    }

    // Next button
    elements.push(
      <li key="next">
        <button
          className="flex size-8 items-center justify-center rounded-[3px] hover:bg-primary hover:text-white disabled:pointer-events-none disabled:opacity-50"
          onClick={goToNextPage}
          disabled={currentPage === totalPages}
        >
          <span className="sr-only">Go to next page</span>
          <svg width="18" height="18" viewBox="0 0 18 18" fill="currentColor"><path d="M5.81953 16.1158C5.65078 16.1158 5.51016 16.0596 5.36953 15.9471C5.11641 15.6939 5.11641 15.3002 5.36953 15.0471L11.2758 9.0002L5.36953 2.98145C5.11641 2.72832 5.11641 2.33457 5.36953 2.08145C5.62266 1.82832 6.01641 1.82832 6.26953 2.08145L12.6258 8.5502C12.8789 8.80332 12.8789 9.19707 12.6258 9.45019L6.26953 15.9189C6.15703 16.0314 5.98828 16.1158 5.81953 16.1158Z"></path></svg>
        </button>
      </li>
    );

    return elements;
  };

  return (
    <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-7.5">
      <div className="flex items-center justify-between mb-6">
        <h4 className="text-xl font-semibold text-black dark:text-white">
          Manajemen Server VPS
        </h4>
        <div className="flex items-center gap-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Cari server..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="flex items-center w-full max-w-xs gap-3.5 rounded-full border bg-gray-2 p-3 pl-10 outline-none ring-primary transition-colors focus-visible:ring-1 dark:border-dark-3 dark:bg-dark-2 dark:hover:border-dark-4 dark:hover:bg-dark-3 dark:hover:text-dark-6"
            />
            <div className="absolute top-1/2 left-3 -translate-y-1/2">
              <svg width="18" height="18" viewBox="0 0 18 18" fill="currentColor" className="max-[1015px]:size-5"><g clipPath="url(#clip0_1699_11536)"><path fillRule="evenodd" clipRule="evenodd" d="M8.625 2.0625C5.00063 2.0625 2.0625 5.00063 2.0625 8.625C2.0625 12.2494 5.00063 15.1875 8.625 15.1875C12.2494 15.1875 15.1875 12.2494 15.1875 8.625C15.1875 5.00063 12.2494 2.0625 8.625 2.0625ZM0.9375 8.625C0.9375 4.37931 4.37931 0.9375 8.625 0.9375C12.8707 0.9375 16.3125 4.37931 16.3125 8.625C16.3125 10.5454 15.6083 12.3013 14.4441 13.6487L16.8977 16.1023C17.1174 16.3219 17.1174 16.6781 16.8977 16.8977C16.6781 17.1174 16.3219 17.1174 16.1023 16.8977L13.6487 14.4441C12.3013 15.6083 10.5454 16.3125 8.625 16.3125C4.37931 16.3125 0.9375 12.8707 0.9375 8.625Z"></path></g><defs><clipPath id="clip0_1699_11536"><rect width="18" height="18" fill="white"></rect></clipPath></defs></svg>
            </div>
          </div>
          <button 
            onClick={initializeAddForm}
            className="inline-flex items-center justify-center rounded-md border border-primary bg-primary px-6 py-2.5 text-center font-medium text-white hover:bg-opacity-90"
          >
            Tambah Server
          </button>
        </div>
      </div>
      
      <Table>
        <TableHeader>
          <TableRow className="border-none bg-[#F7F9FC] dark:bg-dark-2 [&>th]:py-4 [&>th]:text-base [&>th]:text-dark [&>th]:dark:text-white">
            <TableHead className="min-w-[200px] xl:pl-7.5">Server</TableHead>
            <TableHead>Negara</TableHead>
            <TableHead>Harga</TableHead>
            <TableHead>Protokol</TableHead>
            <TableHead>Slot</TableHead>
            <TableHead className="text-right xl:pr-7.5">Aksi</TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          {paginatedServers.map((server) => (
            <TableRow key={server.server_id} className="border-[#eee] dark:border-dark-3">
              <TableCell className="max-w-[22ch] truncate xl:pl-7.5">
                <p className="font-medium text-black dark:text-white">
                  {server.nama}
                </p>
                <p className="text-sm">{server.kode}</p>
                <p className="text-xs text-gray-500">{server.domain}</p>
              </TableCell>

              <TableCell>
                <p className="text-black dark:text-white">{server.negara}</p>
                <p className="text-sm text-gray-500">{server.nama_isp}</p>
              </TableCell>

              <TableCell>
                <p className="text-black dark:text-white">
                  {new Intl.NumberFormat("id-ID", {
                    style: "currency",
                    currency: "IDR",
                    minimumFractionDigits: 0,
                  }).format(server.harga_member)}
                </p>
                <p className="text-xs text-gray-500">
                  Reseller: {new Intl.NumberFormat("id-ID", {
                    style: "currency",
                    currency: "IDR",
                    minimumFractionDigits: 0,
                  }).format(server.harga_reseller)}
                </p>
              </TableCell>

              <TableCell>
                <div className="flex flex-wrap gap-1">
                  <span className={`inline-flex rounded-full px-2 py-0.5 text-[10px] font-medium ${server.ssh === 'enabled' ? 'bg-blue-600 text-white' : 'bg-gray-600 text-gray-300'}`}>
                    SSH
                  </span>
                  <span className={`inline-flex rounded-full px-2 py-0.5 text-[10px] font-medium ${server.trojan === 'enabled' ? 'bg-green-600 text-white' : 'bg-gray-600 text-gray-300'}`}>
                    Trojan
                  </span>
                  <span className={`inline-flex rounded-full px-2 py-0.5 text-[10px] font-medium ${server.vmess === 'enabled' ? 'bg-yellow-600 text-white' : 'bg-gray-600 text-gray-300'}`}>
                    Vmess
                  </span>
                  <span className={`inline-flex rounded-full px-2 py-0.5 text-[10px] font-medium ${server.vless === 'enabled' ? 'bg-red-600 text-white' : 'bg-gray-600 text-gray-300'}`}>
                    Vless
                  </span>
                </div>
              </TableCell>

              <TableCell>
                <p className="text-black dark:text-white">
                  {server.slot_terpakai} / {server.slot_server}
                </p>
                <p className="text-xs text-gray-500">
                  Total User: {server.total_user || 0}
                </p>
              </TableCell>

              <TableCell className="xl:pr-7.5">
                <div className="flex items-center justify-end gap-x-3.5">
                  <button onClick={() => handleViewServer(server)} className="hover:text-primary">
                    <span className="sr-only">Lihat Server</span>
                    <EyeIcon />
                  </button>
                  <button onClick={() => handleEditServer(server)} className="hover:text-primary">
                    <span className="sr-only">Edit Server</span>
                    <EditIcon />
                  </button>
                  <button onClick={() => handleTestToken(server.server_id)} className="hover:text-primary">
                    <span className="sr-only">Test Token</span>
                    <TestTubeIcon />
                  </button>
                  <button onClick={() => handleDeleteServer(server)} className="hover:text-danger">
                    <span className="sr-only">Hapus Server</span>
                    <TrashIcon />
                  </button>
                </div>
              </TableCell>
            </TableRow>
          ))}

          {paginatedServers.length === 0 && (
            <TableRow>
              <TableCell colSpan={6} className="text-center py-10">
                <p className="text-gray-500 dark:text-gray-400">Tidak ada data server tersedia</p>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {/* Pagination */}
      {filteredServers.length > 0 && (
        <div className="p-4 sm:p-6 xl:p-7.5">
          <nav>
            <ul className="flex flex-wrap items-center gap-1">
              {getPaginationLinks()}
            </ul>
          </nav>
        </div>
      )}

      {/* View Server Modal */}
      {selectedServer && (
        <Modal
          isOpen={isViewModalOpen}
          onClose={() => setIsViewModalOpen(false)}
          title="Detail Server"
        >
          <div className="space-y-6 text-body-sm">
            <div className="grid grid-cols-1 gap-x-6 gap-y-4 sm:grid-cols-2">
              <div>
                <p className="font-semibold text-black dark:text-white">Nama</p>
                <span>{selectedServer.nama}</span>
              </div>
              <div>
                <p className="font-semibold text-black dark:text-white">Kode</p>
                <span>{selectedServer.kode}</span>
              </div>
              <div>
                <p className="font-semibold text-black dark:text-white">Domain</p>
                <span>{selectedServer.domain}</span>
              </div>
              <div>
                <p className="font-semibold text-black dark:text-white">Token</p>
                <div className="flex items-center gap-2">
                  <span>{isTokenVisible ? selectedServer.token : "********************"}</span>
                  <button onClick={() => setIsTokenVisible(!isTokenVisible)} className="hover:text-primary">
                    {isTokenVisible ? <EyeOffIcon /> : <EyeIcon />}
                  </button>
                </div>
              </div>
              <div>
                <p className="font-semibold text-black dark:text-white">Negara</p>
                <span>{selectedServer.negara}</span>
              </div>
              <div>
                <p className="font-semibold text-black dark:text-white">ISP</p>
                <span>{selectedServer.nama_isp}</span>
              </div>
              <div>
                <p className="font-semibold text-black dark:text-white">Harga Member</p>
                <span>{new Intl.NumberFormat("id-ID", { style: "currency", currency: "IDR", minimumFractionDigits: 0 }).format(selectedServer.harga_member)}</span>
              </div>
              <div>
                <p className="font-semibold text-black dark:text-white">Harga Reseller</p>
                <span>{new Intl.NumberFormat("id-ID", { style: "currency", currency: "IDR", minimumFractionDigits: 0 }).format(selectedServer.harga_reseller)}</span>
              </div>
              <div>
                <p className="font-semibold text-black dark:text-white">Slot Server</p>
                <span>{selectedServer.slot_terpakai} / {selectedServer.slot_server}</span>
              </div>
              <div>
                <p className="font-semibold text-black dark:text-white">Total User</p>
                <span>{selectedServer.total_user}</span>
              </div>
              <div>
                <p className="font-semibold text-black dark:text-white">Max Device</p>
                <span>{selectedServer.max_device}</span>
              </div>
              <div>
                <p className="font-semibold text-black dark:text-white">Tanggal Dibuat</p>
                <span>{dayjs(selectedServer.created_at).format("dddd, MMMM D, YYYY h:mm A")}</span>
              </div>
            </div>

            <div className="mt-4">
              <p className="font-semibold text-black dark:text-white mb-2">Protokol</p>
              <div className="flex flex-wrap gap-2">
                <span className={`inline-flex rounded px-2 py-0.5 text-[10px] font-medium ${selectedServer.ssh === 'enabled' ? 'bg-blue-600 text-white' : 'bg-gray-600 text-gray-300'}`}>
                  SSH
                </span>
                <span className={`inline-flex rounded px-2 py-0.5 text-[10px] font-medium ${selectedServer.trojan === 'enabled' ? 'bg-green-600 text-white' : 'bg-gray-600 text-gray-300'}`}>
                  Trojan
                </span>
                <span className={`inline-flex rounded px-2 py-0.5 text-[10px] font-medium ${selectedServer.vmess === 'enabled' ? 'bg-yellow-600 text-white' : 'bg-gray-600 text-gray-300'}`}>
                  Vmess
                </span>
                <span className={`inline-flex rounded px-2 py-0.5 text-[10px] font-medium ${selectedServer.vless === 'enabled' ? 'bg-red-600 text-white' : 'bg-gray-600 text-gray-300'}`}>
                  Vless
                </span>
              </div>
            </div>
          </div>
        </Modal>
      )}

      {/* Add Server Modal */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Tambah Server Baru"
      >
        <form onSubmit={handleAddSubmit} className="max-h-[70vh] overflow-y-auto overflow-x-hidden pr-2">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="mb-4">
              <label className="form-label">Nama Server</label>
              <input 
                type="text" 
                name="nama" 
                value={formData.nama} 
                onChange={handleInputChange} 
                className="form-input" 
                required 
              />
            </div>
            <div className="mb-4">
              <label className="form-label">Kode</label>
              <input 
                type="text" 
                name="kode" 
                value={formData.kode} 
                onChange={handleInputChange} 
                className="form-input" 
                required 
              />
            </div>
            <div className="mb-4">
              <label className="form-label">Domain</label>
              <input 
                type="text" 
                name="domain" 
                value={formData.domain} 
                onChange={handleInputChange} 
                className="form-input" 
                required 
              />
            </div>
            <div className="mb-4">
              <label className="form-label">Token</label>
              <input 
                type="password" 
                name="token" 
                value={formData.token} 
                onChange={handleInputChange} 
                className="form-input" 
                required 
              />
            </div>
            <div className="mb-4">
              <label className="form-label">Negara</label>
              <select
                name="negara" 
                value={formData.negara} 
                onChange={handleInputChange} 
                className="form-input" 
                required
              >
                <option value="Indonesia">Indonesia</option>
                <option value="Singapore">Singapore</option>
              </select>
            </div>
            <div className="mb-4">
              <label className="form-label">Nama ISP</label>
              <input 
                type="text" 
                name="nama_isp" 
                value={formData.nama_isp} 
                onChange={handleInputChange} 
                className="form-input" 
                required 
              />
            </div>
            <div className="mb-4">
              <label className="form-label">Harga Member</label>
              <input 
                type="number" 
                name="harga_member" 
                value={formData.harga_member} 
                onChange={handleInputChange} 
                className="form-input" 
                required 
                min="0" 
              />
            </div>
            <div className="mb-4">
              <label className="form-label">Harga Reseller</label>
              <input 
                type="number" 
                name="harga_reseller" 
                value={formData.harga_reseller} 
                onChange={handleInputChange} 
                className="form-input" 
                required 
                min="0" 
              />
            </div>
            <div className="mb-4">
              <label className="form-label">SSH</label>
              <select
                name="ssh" 
                value={formData.ssh} 
                onChange={handleInputChange} 
                className="form-input"
              >
                <option value="disabled">Disable</option>
                <option value="enabled">Enable</option>
              </select>
            </div>
            <div className="mb-4">
              <label className="form-label">Trojan</label>
              <select
                name="trojan" 
                value={formData.trojan} 
                onChange={handleInputChange} 
                className="form-input"
              >
                <option value="disabled">Disable</option>
                <option value="enabled">Enable</option>
              </select>
            </div>
            <div className="mb-4">
              <label className="form-label">Vmess</label>
              <select
                name="vmess" 
                value={formData.vmess} 
                onChange={handleInputChange} 
                className="form-input"
              >
                <option value="disabled">Disable</option>
                <option value="enabled">Enable</option>
              </select>
            </div>
            <div className="mb-4">
              <label className="form-label">Vless</label>
              <select
                name="vless" 
                value={formData.vless} 
                onChange={handleInputChange} 
                className="form-input"
              >
                <option value="disabled">Disable</option>
                <option value="enabled">Enable</option>
              </select>
            </div>
            <div className="mb-4">
              <label className="form-label">Slot Server</label>
              <input 
                type="number" 
                name="slot_server" 
                value={formData.slot_server} 
                onChange={handleInputChange} 
                className="form-input" 
                required 
                min="1" 
              />
            </div>
            <div className="mb-4">
              <label className="form-label">Max Device</label>
              <input 
                type="number" 
                name="max_device" 
                value={formData.max_device} 
                onChange={handleInputChange} 
                className="form-input" 
                required 
                min="1" 
              />
            </div>
          </div>
          <div className="flex justify-end gap-3 mt-6">
            <button
              type="button"
              onClick={handleTestNewToken}
              className="form-button-secondary"
              disabled={isSubmitting}
            >
              Test Token
            </button>
            <button
              type="button"
              onClick={() => setIsAddModalOpen(false)}
              className="form-button-secondary"
              disabled={isSubmitting}
            >
              Batal
            </button>
            <button
              type="submit"
              className="form-button-primary"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Menyimpan...' : 'Tambah Server'}
            </button>
          </div>
        </form>
      </Modal>

      {/* Edit Server Modal */}
      {selectedServer && (
        <Modal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          title="Edit Server"
        >
          <form onSubmit={handleEditSubmit} className="max-h-[70vh] overflow-y-auto overflow-x-hidden pr-2">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="mb-4">
                <label className="form-label">Nama Server</label>
                <input 
                  type="text" 
                  name="nama" 
                  value={formData.nama} 
                  onChange={handleInputChange} 
                  className="form-input" 
                  required 
                />
              </div>
              <div className="mb-4">
                <label className="form-label">Kode</label>
                <input 
                  type="text" 
                  name="kode" 
                  value={formData.kode} 
                  onChange={handleInputChange} 
                  className="form-input" 
                  required 
                  disabled
                />
                <span className="text-xs text-gray-500">Kode tidak dapat diubah</span>
              </div>
              <div className="mb-4">
                <label className="form-label">Domain</label>
                <input 
                  type="text" 
                  name="domain" 
                  value={formData.domain} 
                  onChange={handleInputChange} 
                  className="form-input" 
                  required 
                />
              </div>
              <div className="mb-4">
                <label className="form-label">Token</label>
                <input
                  type="password"
                  name="token"
                  value={formData.token}
                  onChange={handleInputChange}
                  className="form-input"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="form-label">Negara</label>
                <select
                  name="negara" 
                  value={formData.negara} 
                  onChange={handleInputChange} 
                  className="form-input" 
                  required
                >
                  <option value="Indonesia">Indonesia</option>
                  <option value="Singapore">Singapore</option>
                </select>
              </div>
              <div className="mb-4">
                <label className="form-label">Nama ISP</label>
                <input 
                  type="text" 
                  name="nama_isp" 
                  value={formData.nama_isp} 
                  onChange={handleInputChange} 
                  className="form-input" 
                  required 
                />
              </div>
              <div className="mb-4">
                <label className="form-label">Harga Member</label>
                <input 
                  type="number" 
                  name="harga_member" 
                  value={formData.harga_member} 
                  onChange={handleInputChange} 
                  className="form-input" 
                  required 
                  min="0" 
                />
              </div>
              <div className="mb-4">
                <label className="form-label">Harga Reseller</label>
                <input 
                  type="number" 
                  name="harga_reseller" 
                  value={formData.harga_reseller} 
                  onChange={handleInputChange} 
                  className="form-input" 
                  required 
                  min="0" 
                />
              </div>
              <div className="mb-4">
                <label className="form-label">SSH</label>
                <select
                  name="ssh" 
                  value={formData.ssh} 
                  onChange={handleInputChange} 
                  className="form-input"
                >
                  <option value="disabled">Disable</option>
                  <option value="enabled">Enable</option>
                </select>
              </div>
              <div className="mb-4">
                <label className="form-label">Trojan</label>
                <select
                  name="trojan" 
                  value={formData.trojan} 
                  onChange={handleInputChange} 
                  className="form-input"
                >
                  <option value="disabled">Disable</option>
                  <option value="enabled">Enable</option>
                </select>
              </div>
              <div className="mb-4">
                <label className="form-label">Vmess</label>
                <select
                  name="vmess" 
                  value={formData.vmess} 
                  onChange={handleInputChange} 
                  className="form-input"
                >
                  <option value="disabled">Disable</option>
                  <option value="enabled">Enable</option>
                </select>
              </div>
              <div className="mb-4">
                <label className="form-label">Vless</label>
                <select
                  name="vless" 
                  value={formData.vless} 
                  onChange={handleInputChange} 
                  className="form-input"
                >
                  <option value="disabled">Disable</option>
                  <option value="enabled">Enable</option>
                </select>
              </div>
              <div className="mb-4">
                <label className="form-label">Slot Server</label>
                <input 
                  type="number" 
                  name="slot_server" 
                  value={formData.slot_server} 
                  onChange={handleInputChange} 
                  className="form-input" 
                  required 
                  min="1" 
                />
              </div>
              <div className="mb-4">
                <label className="form-label">Slot Terpakai</label>
                <input 
                  type="number" 
                  name="slot_terpakai" 
                  value={formData.slot_terpakai} 
                  onChange={handleInputChange} 
                  className="form-input" 
                  min="0" 
                />
              </div>
              <div className="mb-4">
                <label className="form-label">Total User</label>
                <input 
                  type="number" 
                  name="total_user" 
                  value={formData.total_user} 
                  className="form-input bg-gray-100 cursor-not-allowed" 
                  disabled
                  readOnly
                />
                <span className="text-xs text-gray-500">Total user dikelola oleh sistem</span>
              </div>
              <div className="mb-4">
                <label className="form-label">Max Device</label>
                <input 
                  type="number" 
                  name="max_device" 
                  value={formData.max_device} 
                  onChange={handleInputChange} 
                  className="form-input" 
                  required 
                  min="1" 
                />
              </div>
            </div>
            <div className="flex justify-end gap-3 mt-6">
                <button
                  type="button"
                  onClick={() => setIsEditModalOpen(false)}
                  className="px-4 py-2 rounded bg-gray-2 hover:bg-gray-3 dark:bg-dark-2 dark:hover:bg-dark-3"
                  disabled={isSubmitting}
                >
                  Batal
                </button>
                <button
                  type="submit"
                  className="form-button-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Menyimpan...' : 'Perbarui Server'}
                </button>
            </div>
          </form>
        </Modal>
      )}

      {/* Delete Confirmation Modal */}
      {selectedServer && (
        <Modal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          title="Konfirmasi Hapus"
        >
          <div className="space-y-6">
            <p className="text-center">
              Apakah Anda yakin ingin menghapus server <strong>{selectedServer.nama}</strong>?
            </p>
            <p className="text-center text-danger">
              Tindakan ini tidak dapat dibatalkan.
            </p>
            <div className="flex justify-end gap-3 mt-6">
              <button
                type="button"
                onClick={() => setIsDeleteModalOpen(false)}
                className="form-button-secondary"
                disabled={isSubmitting}
              >
                Batal
              </button>
              <button
                type="button"
                onClick={handleDeleteSubmit}
                className="form-button-danger"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Menghapus...' : 'Hapus Server'}
              </button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default ServerTable;
