"use client";

import { useState, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { PublicTransaction } from '@/types/transaction';
import dayjs from 'dayjs';
import { cn } from '@/lib/utils';
import { debounce } from 'lodash';
import { standardFormat } from '@/lib/format-number';
import Link from 'next/link';
import { Eye } from 'lucide-react';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

const ITEMS_PER_PAGE = 10;

export interface TopUpHistoryApiResponse {
  data: PublicTransaction[];
  pagination: {
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
  };
  message?: string;
}

interface TopUpHistoryTableProps {
  initialData: TopUpHistoryApiResponse;
}

const TopUpHistoryTable = ({ initialData }: TopUpHistoryTableProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const history = initialData.data || [];
  const totalData = initialData.pagination?.total || 0;
  const currentPage = initialData.pagination?.current_page || 1;
  const totalPages = initialData.pagination?.last_page || Math.ceil(totalData / ITEMS_PER_PAGE);

  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || "");

  const debouncedNavigate = useCallback(debounce((query: string, page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', String(page));
    if (query) {
      params.set('search', query);
    } else {
      params.delete('search');
    }
    router.replace(`/top-up/history?${params.toString()}`);
  }, 500), [router, searchParams]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = event.target.value;
    setSearchQuery(newQuery);
    debouncedNavigate(newQuery, 1); // Reset to page 1 for new search
  };

  const goToPage = (page: number) => {
    debouncedNavigate(searchQuery, page);
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  };

  const getPaginationLinks = () => {
    const links = [];
    links.push(
      <li key="prev">
        <button
          className="flex size-8 items-center justify-center rounded-[3px] hover:bg-primary hover:text-white disabled:pointer-events-none disabled:opacity-50"
          onClick={goToPreviousPage}
          disabled={currentPage === 1}
        >
          <span className="sr-only">Go to previous page</span>
          <svg width="18" height="18" viewBox="0 0 18 18" fill="currentColor"><path d="M12.1758 16.1158C12.007 16.1158 11.8383 16.0596 11.7258 15.9189L5.36953 9.45019C5.11641 9.19707 5.11641 8.80332 5.36953 8.5502L11.7258 2.08145C11.9789 1.82832 12.3727 1.82832 12.6258 2.08145C12.8789 2.33457 12.8789 2.72832 12.6258 2.98145L6.71953 9.0002L12.6539 15.0189C12.907 15.2721 12.907 15.6658 12.6539 15.9189C12.4852 16.0314 12.3445 16.1158 12.1758 16.1158Z"></path></svg>
        </button>
      </li>
    );

    for (let i = 1; i <= totalPages; i++) {
        if (totalPages <= 5 || (i >= currentPage - 2 && i <= currentPage + 2) || i === 1 || i === totalPages) {
             links.push(
                <li key={i}>
                    <button
                    className={cn("flex items-center justify-center rounded-[3px] px-3 py-1.5 font-medium hover:bg-primary hover:text-white", { 'bg-primary text-white': currentPage === i })}
                    onClick={() => goToPage(i)}
                    >
                    {i}
                    </button>
                </li>
            );
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            links.push(<li key={`ellipsis-${i}`}><span className="flex items-center justify-center px-3 py-1.5">...</span></li>);
        }
    }

    links.push(
      <li key="next">
        <button
          className="flex size-8 items-center justify-center rounded-[3px] hover:bg-primary hover:text-white disabled:pointer-events-none disabled:opacity-50"
          onClick={goToNextPage}
          disabled={currentPage === totalPages}
        >
          <span className="sr-only">Go to next page</span>
          <svg width="18" height="18" viewBox="0 0 18 18" fill="currentColor"><path d="M5.81953 16.1158C5.65078 16.1158 5.51016 16.0596 5.36953 15.9471C5.11641 15.6939 5.11641 15.3002 5.36953 15.0471L11.2758 9.0002L5.36953 2.98145C5.11641 2.72832 5.11641 2.33457 5.36953 2.08145C5.62266 1.82832 6.01641 1.82832 6.26953 2.08145L12.6258 8.5502C12.8789 8.80332 12.8789 9.19707 12.6258 9.45019L6.26953 15.9189C6.15703 16.0314 5.98828 16.1158 5.81953 16.1158Z"></path></svg>
        </button>
      </li>
    );

    return links;
  };

  return (
    <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-7.5 overflow-x-auto">
      <div className="flex items-center justify-between mb-6">
        <h4 className="text-xl font-semibold text-black dark:text-white">
          Riwayat Top Up
        </h4>
        <div className="relative">
          <input
            type="text"
            placeholder="Cari riwayat..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="flex items-center w-full max-w-xs gap-3.5 rounded-full border bg-gray-2 p-3 pl-10 outline-none ring-primary transition-colors focus-visible:ring-1 dark:border-dark-3 dark:bg-dark-2 dark:hover:border-dark-4 dark:hover:bg-dark-3 dark:hover:text-dark-6"
          />
          <div className="absolute top-1/2 left-3 -translate-y-1/2">
            <svg width="18" height="18" viewBox="0 0 18 18" fill="currentColor" className="max-[1015px]:size-5"><g clipPath="url(#clip0_1699_11536)"><path fillRule="evenodd" clipRule="evenodd" d="M8.625 2.0625C5.00063 2.0625 2.0625 5.00063 2.0625 8.625C2.0625 12.2494 5.00063 15.1875 8.625 15.1875C12.2494 15.1875 15.1875 12.2494 15.1875 8.625C15.1875 5.00063 12.2494 2.0625 8.625 2.0625ZM0.9375 8.625C0.9375 4.37931 4.37931 0.9375 8.625 0.9375C12.8707 0.9375 16.3125 4.37931 16.3125 8.625C16.3125 10.5454 15.6083 12.3013 14.4441 13.6487L16.8977 16.1023C17.1174 16.3219 17.1174 16.6781 16.8977 16.8977C16.6781 17.1174 16.3219 17.1174 16.1023 16.8977L13.6487 14.4441C12.3013 15.6083 10.5454 16.3125 8.625 16.3125C4.37931 16.3125 0.9375 12.8707 0.9375 8.625Z"></path></g><defs><clipPath id="clip0_1699_11536"><rect width="18" height="18" fill="white"></rect></clipPath></defs></svg>
          </div>
        </div>
      </div>
      <Table className="w-full">
        <TableHeader>
          <TableRow className="border-none bg-[#F7F9FC] dark:bg-dark-2 [&>th]:py-4 [&>th]:text-base [&>th]:text-dark [&>th]:dark:text-white">
            <TableHead className="w-[20%] xl:pl-7.5">Invoice ID</TableHead>
            <TableHead className="w-[20%]">Deskripsi</TableHead>
            <TableHead className="w-[15%]">Tanggal</TableHead>
            <TableHead className="w-[15%]">Status</TableHead>
            <TableHead className="w-[15%] text-right">Jumlah</TableHead>
            <TableHead className="w-[15%] text-center">Aksi</TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          {history.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-10">
                {initialData.message || 'Tidak ada riwayat top up ditemukan.'}
              </TableCell>
              </TableRow>
            ) : (history.map((item) => (
            <TableRow key={item.id} className="border-b border-[#eee] dark:border-dark-3 [&>td]:py-5">
              <TableCell className="xl:pl-7.5">
                <Link href={`/product/order/invoice?invoice_id=${item.invoice_id}`} className="font-medium text-black dark:text-white whitespace-nowrap overflow-hidden text-ellipsis hover:text-primary dark:hover:text-primary cursor-pointer">
                  {item.invoice_id}
                </Link>
              </TableCell>

              <TableCell>
                <p className="text-black dark:text-white whitespace-nowrap overflow-hidden text-ellipsis">
                  {item.description}
                </p>
              </TableCell>
              
              <TableCell>
                <p className="text-black dark:text-white whitespace-nowrap overflow-hidden text-ellipsis">{dayjs(item.created_at).format('DD MMM YYYY HH:mm')}</p>
              </TableCell>
              
              <TableCell>
                <span className={`badge-pill-outline inline-flex items-center gap-1.5 ${item.status === 'SUCCESS' ? 'badge-pill-outline-success' : item.status === 'PENDING' ? 'badge-pill-outline-warning' : 'badge-pill-outline-danger'}`}>
                  {item.status === 'SUCCESS' && (
                    <svg className="h-3.5 w-3.5 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8.79508 15.8749L4.62508 11.7049L3.20508 13.1149L8.79508 18.7049L20.7951 6.70492L19.3851 5.29492L8.79508 15.8749Z" fill="currentColor" />
                    </svg>
                  )}
                  {item.status === 'PENDING' && (
                    <svg className="h-3.5 w-3.5 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM12.5 7H11V13L16.2 16.2L17 14.9L12.5 12.2V7Z" fill="currentColor" />
                    </svg>
                  )}
                  {item.status === 'FAILED' && (
                    <svg className="h-3.5 w-3.5 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM13 7H11V13H13V7ZM13 15H11V17H13V15Z" fill="currentColor" />
                    </svg>
                  )}
                  {item.status === 'SUCCESS' ? 'Sukses' : item.status === 'PENDING' ? 'Pending' : 'Gagal'}
                </span>
              </TableCell>

              <TableCell className="text-right">
                <p className="text-green-light-1 font-medium whitespace-nowrap overflow-hidden text-ellipsis">Rp {standardFormat(item.amount)}</p>
              </TableCell>
              
              <TableCell className="text-center xl:pr-7.5">
                <div className="flex items-center justify-center">
                  <Link href={`/product/order/invoice?invoice_id=${item.invoice_id}`} className="hover:text-primary" title="Lihat Invoice">
                    <Eye size={20} />
                  </Link>
                </div>
              </TableCell>
            </TableRow>
          )))}
        </TableBody>
      </Table>

      {/* Pagination */}
      <div className="p-4 sm:p-6 xl:p-7.5">
        <nav>
          <ul className="flex flex-wrap items-center gap-1">
            {getPaginationLinks()}
          </ul>
        </nav>
      </div>
    </div>
  );
};

export default TopUpHistoryTable;