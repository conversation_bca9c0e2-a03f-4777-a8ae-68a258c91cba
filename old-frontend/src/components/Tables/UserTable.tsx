"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import dayjs from "dayjs";

import { EyeIcon, EditIcon } from "../Layouts/sidebar/icons";
import { useState, useMemo } from "react";
import { useRouter } from 'next/navigation';
import Modal from "../Modals/Modal"; // Import the modal component
import { useNotification } from "@/contexts/NotificationContext";

// Define a type for the user prop for better type safety
type User = {
  id: number;
  name: string;
  username: string;
  email: string;
  saldo: number;
  roles: { name: string }[];
  created_at: string;
  suspend: string | null;
  email_verified: boolean;
  whatsapp: string | null;
  verif_wa: boolean;
  account_type: string;
};

const ITEMS_PER_PAGE = 10;

const UserTable = ({ users }: { users: User[] }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editFormData, setEditFormData] = useState({ email: '', whatsapp: '', saldo: 0 });
  const { addNotification } = useNotification();
  const router = useRouter();

  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setIsViewModalOpen(true);
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setEditFormData({ 
      email: user.email,
      whatsapp: user.whatsapp || '',
      saldo: user.saldo
    });
    setIsEditModalOpen(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEditFormData(prev => ({ ...prev, [name]: value }));
  }

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedUser) return;

    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(editFormData.email)) {
      addNotification('Format email tidak valid. Silakan masukkan email yang valid.', 'error');
      return;
    }

    try {
      const res = await fetch(`/api/users/${selectedUser.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...editFormData,
          saldo: Number(editFormData.saldo) // Ensure saldo is a number
        }),
      });

      const data = await res.json();

      if (!res.ok) {
        throw new Error(data.message || 'Gagal memperbarui pengguna.');
      }

      addNotification('Pengguna berhasil diperbarui!', 'success');
      setIsEditModalOpen(false);
      router.refresh();

    } catch (error: any) {
      addNotification(error.message, 'error');
    }
  };

  const filteredUsers = useMemo(() => {
    return users
      .filter(
        (user) =>
          user.roles && !user.roles.some((role) => role.name === "admin")
      )
      .filter((user) => {
        if (!searchQuery) return true; // Keep user if no search query
        // Filter by search query
        return (
          (user.name &&
            user.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
          (user.username &&
            user.username.toLowerCase().includes(searchQuery.toLowerCase())) ||
          (user.email &&
            user.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
          (user.whatsapp &&
            user.whatsapp.toLowerCase().includes(searchQuery.toLowerCase()))
        );
      });
  }, [users, searchQuery]);

  const paginatedUsers = useMemo(() => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    return filteredUsers.slice(startIndex, endIndex);
  }, [filteredUsers, currentPage]);

  const totalPages = Math.ceil(filteredUsers.length / ITEMS_PER_PAGE);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
    setCurrentPage(1); // Reset to the first page on a new search
  };

  const goToPage = (page: number) => {
    setCurrentPage(page);
  };

  const goToNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const goToPreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  // Function to generate pagination links
  const getPaginationLinks = () => {
    const links = [];
    // Previous button
    links.push(
      <li key="prev">
        <button
          className="flex size-8 items-center justify-center rounded-[3px] hover:bg-primary hover:text-white disabled:pointer-events-none disabled:opacity-50"
          onClick={goToPreviousPage}
          disabled={currentPage === 1}
        >
          <span className="sr-only">Go to previous page</span>
          <svg width="18" height="18" viewBox="0 0 18 18" fill="currentColor"><path d="M12.1758 16.1158C12.007 16.1158 11.8383 16.0596 11.7258 15.9189L5.36953 9.45019C5.11641 9.19707 5.11641 8.80332 5.36953 8.5502L11.7258 2.08145C11.9789 1.82832 12.3727 1.82832 12.6258 2.08145C12.8789 2.33457 12.8789 2.72832 12.6258 2.98145L6.71953 9.0002L12.6539 15.0189C12.907 15.2721 12.907 15.6658 12.6539 15.9189C12.4852 16.0314 12.3445 16.1158 12.1758 16.1158Z"></path></svg>
        </button>
      </li>
    );

    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        // This logic is simplified. For a large number of pages, you'd want to add ellipses (...)
        if (totalPages <= 5 || (i >= currentPage - 2 && i <= currentPage + 2) || i === 1 || i === totalPages) {
             links.push(
                <li key={i}>
                    <button
                    className={cn("flex items-center justify-center rounded-[3px] px-3 py-1.5 font-medium hover:bg-primary hover:text-white", { 'bg-primary text-white': currentPage === i })}
                    onClick={() => goToPage(i)}
                    >
                    {i}
                    </button>
                </li>
            );
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            links.push(<li key={`ellipsis-${i}`}><span className="flex items-center justify-center px-3 py-1.5">...</span></li>);
        }
    }

    // Next button
    links.push(
      <li key="next">
        <button
          className="flex size-8 items-center justify-center rounded-[3px] hover:bg-primary hover:text-white disabled:pointer-events-none disabled:opacity-50"
          onClick={goToNextPage}
          disabled={currentPage === totalPages}
        >
          <span className="sr-only">Go to next page</span>
          <svg width="18" height="18" viewBox="0 0 18 18" fill="currentColor"><path d="M5.81953 16.1158C5.65078 16.1158 5.51016 16.0596 5.36953 15.9471C5.11641 15.6939 5.11641 15.3002 5.36953 15.0471L11.2758 9.0002L5.36953 2.98145C5.11641 2.72832 5.11641 2.33457 5.36953 2.08145C5.62266 1.82832 6.01641 1.82832 6.26953 2.08145L12.6258 8.5502C12.8789 8.80332 12.8789 9.19707 12.6258 9.45019L6.26953 15.9189C6.15703 16.0314 5.98828 16.1158 5.81953 16.1158Z"></path></svg>
        </button>
      </li>
    );

    return links;
  };

  return (
    <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-7.5">
      <div className="flex items-center justify-between mb-6">
        <h4 className="text-xl font-semibold text-black dark:text-white">
          Semua Pengguna
        </h4>
        <div className="relative">
          <input
            type="text"
            placeholder="Cari pengguna..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="flex items-center w-full max-w-xs gap-3.5 rounded-full border bg-gray-2 p-3 pl-10 outline-none ring-primary transition-colors focus-visible:ring-1 dark:border-dark-3 dark:bg-dark-2 dark:hover:border-dark-4 dark:hover:bg-dark-3 dark:hover:text-dark-6"
          />
          <div className="absolute top-1/2 left-3 -translate-y-1/2">
            <svg width="18" height="18" viewBox="0 0 18 18" fill="currentColor" className="max-[1015px]:size-5"><g clipPath="url(#clip0_1699_11536)"><path fillRule="evenodd" clipRule="evenodd" d="M8.625 2.0625C5.00063 2.0625 2.0625 5.00063 2.0625 8.625C2.0625 12.2494 5.00063 15.1875 8.625 15.1875C12.2494 15.1875 15.1875 12.2494 15.1875 8.625C15.1875 5.00063 12.2494 2.0625 8.625 2.0625ZM0.9375 8.625C0.9375 4.37931 4.37931 0.9375 8.625 0.9375C12.8707 0.9375 16.3125 4.37931 16.3125 8.625C16.3125 10.5454 15.6083 12.3013 14.4441 13.6487L16.8977 16.1023C17.1174 16.3219 17.1174 16.6781 16.8977 16.8977C16.6781 17.1174 16.3219 17.1174 16.1023 16.8977L13.6487 14.4441C12.3013 15.6083 10.5454 16.3125 8.625 16.3125C4.37931 16.3125 0.9375 12.8707 0.9375 8.625Z"></path></g><defs><clipPath id="clip0_1699_11536"><rect width="18" height="18" fill="white"></rect></clipPath></defs></svg>
          </div>
        </div>
      </div>
      <Table>
        <TableHeader>
          <TableRow className="border-none bg-[#F7F9FC] dark:bg-dark-2 [&>th]:py-4 [&>th]:text-base [&>th]:text-dark [&>th]:dark:text-white">
            <TableHead className="min-w-[200px] xl:pl-7.5">Pengguna</TableHead>
            <TableHead>Saldo</TableHead>
            <TableHead>WhatsApp</TableHead>
            <TableHead>Peran</TableHead>
            <TableHead>Tanggal Bergabung</TableHead>
            <TableHead className="text-right xl:pr-7.5">Aksi</TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          {paginatedUsers.map((user) => (
            <TableRow key={user.id} className="border-[#eee] dark:border-dark-3">
              <TableCell className="max-w-[22ch] truncate xl:pl-7.5">
                <p className="font-medium text-black dark:text-white">
                  {user.name}
                </p>
                <p className="text-sm">{user.username}</p>
                <p className="text-xs text-gray-500">{user.email}</p>
              </TableCell>

              <TableCell>
                <p className="text-black dark:text-white">
                  {new Intl.NumberFormat("id-ID", {
                    style: "currency",
                    currency: "IDR",
                    minimumFractionDigits: 0,
                  }).format(user.saldo)}
                </p>
              </TableCell>
              <TableCell>
                <p className="text-black dark:text-white">{user.whatsapp || '-'}</p>
              </TableCell>

              <TableCell>
                <div className="flex flex-wrap gap-1">
                  {user.roles.map((role) => (
                    <p
                      key={role.name}
                      className={cn(
                        "inline-flex rounded-full bg-opacity-10 px-3 py-1 text-sm font-medium",
                        {
                          "bg-primary text-primary": role.name === 'admin',
                          "bg-success text-success": role.name === 'user',
                          "bg-warning text-warning": role.name !== 'admin' && role.name !== 'user',
                        }
                      )}
                    >
                      {role.name}
                    </p>
                  ))}
                </div>
              </TableCell>

              <TableCell>
                <p className="text-black dark:text-white">
                  {dayjs(user.created_at).format("MMM DD, YYYY")}
                </p>
              </TableCell>

              <TableCell className="xl:pr-7.5">
                <div className="flex items-center justify-end gap-x-3.5">
                  <button onClick={() => handleViewUser(user)} className="hover:text-primary">
                    <span className="sr-only">Lihat Pengguna</span>
                    <EyeIcon />
                  </button>
                  <button onClick={() => handleEditUser(user)} className="hover:text-primary">
                    <span className="sr-only">Edit Pengguna</span>
                    <EditIcon />
                  </button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Pagination */}
      <div className="p-4 sm:p-6 xl:p-7.5">
        <nav>
          <ul className="flex flex-wrap items-center gap-1">
            {getPaginationLinks()}
          </ul>
        </nav>
      </div>

      {/* View User Modal */}
      {selectedUser && (
        <Modal
          isOpen={isViewModalOpen}
          onClose={() => setIsViewModalOpen(false)}
          title="Detail Pengguna"
        >
          <div className="space-y-6 text-body-sm">
            <div>
              <p className="font-semibold text-black dark:text-white mb-2">Peran</p>
              <div className="flex flex-wrap gap-2">
                {selectedUser.roles.map((role) => (
                    <span
                        key={role.name}
                        className={cn(
                            "inline-flex rounded px-2 py-1 text-sm font-medium text-white",
                            {
                                "bg-primary": role.name === 'admin',
                                "bg-[#3CA745]": role.name === 'user',
                                "bg-dark": role.name !== 'admin' && role.name !== 'user',
                            }
                        )}
                    >
                        {role.name}
                    </span>
                ))}
              </div>
            </div>
            <div className="grid grid-cols-1 gap-x-6 gap-y-4 sm:grid-cols-2">
              <div>
                <p className="font-semibold text-black dark:text-white">Nama</p>
                <span>{selectedUser.name}</span>
              </div>
              <div>
                <p className="font-semibold text-black dark:text-white">Nama Pengguna</p>
                <span>{selectedUser.username}</span>
              </div>
              <div>
                <p className="font-semibold text-black dark:text-white">Email</p>
                <span>{selectedUser.email}</span>
              </div>
              <div>
                <p className="font-semibold text-black dark:text-white">WhatsApp</p>
                <span>{selectedUser.whatsapp || '-'}</span>
              </div>
              <div>
                <p className="font-semibold text-black dark:text-white">Saldo</p>
                <span>{new Intl.NumberFormat("id-ID", { style: "currency", currency: "IDR" }).format(selectedUser.saldo)}</span>
              </div>
              <div>
                <p className="font-semibold text-black dark:text-white">Tanggal Bergabung</p>
                <span>{dayjs(selectedUser.created_at).format("dddd, MMMM D, YYYY h:mm A")}</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
              <div className="text-center">
                <p className="font-semibold text-black dark:text-white mb-2">Email Terverifikasi</p>
                {selectedUser.email_verified ? (
                    <span className="inline-flex items-center justify-center px-3 py-1 text-xs font-medium rounded-full bg-green-500 text-white">
                      <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                      </svg>
                      Terverifikasi
                    </span>
                ) : (
                    <span className="inline-flex items-center justify-center px-3 py-1 text-xs font-medium rounded-full bg-red-500 text-white">
                      <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"></path>
                      </svg>
                      Belum Terverifikasi
                    </span>
                )}
              </div>
               <div className="text-center">
                <p className="font-semibold text-black dark:text-white mb-2">WA Terverifikasi</p>
                {selectedUser.verif_wa ? (
                    <span className="inline-flex items-center justify-center px-3 py-1 text-xs font-medium rounded-full bg-green-500 text-white">
                      <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                      </svg>
                      Terverifikasi
                    </span>
                ) : (
                    <span className="inline-flex items-center justify-center px-3 py-1 text-xs font-medium rounded-full bg-red-500 text-white">
                      <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"></path>
                      </svg>
                      Belum Terverifikasi
                    </span>
                )}
              </div>
              <div className="text-center">
                <p className="font-semibold text-black dark:text-white mb-2">Tipe Akun</p>
                <span className="inline-flex items-center justify-center px-3 py-1 text-xs font-medium rounded-full bg-blue-500 text-white">
                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                    <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"></path>
                  </svg>
                  {selectedUser.account_type}
                </span>
              </div>
               <div className="text-center">
                <p className="font-semibold text-black dark:text-white mb-2">Status</p>
                {selectedUser.suspend ? (
                    <span className="inline-flex items-center justify-center px-3 py-1 text-xs font-medium rounded-full bg-yellow-500 text-white">
                      <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd"></path>
                      </svg>
                      Ditangguhkan
                    </span>
                ) : (
                    <span className="inline-flex items-center justify-center px-3 py-1 text-xs font-medium rounded-full bg-green-500 text-white">
                      <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                      </svg>
                      Aktif
                    </span>
                )}
              </div>
            </div>
          </div>
        </Modal>
      )}

      {/* Edit User Modal */}
      {selectedUser && (
        <Modal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          title="Edit Pengguna"
        >
          <form onSubmit={handleFormSubmit}>
            <div className="mb-4">
              <label className="form-label">Email</label>
              <input type="email" name="email" value={editFormData.email} onChange={handleInputChange} className="form-input" />
            </div>
            <div className="mb-4">
              <label className="form-label">WhatsApp</label>
              <input type="text" name="whatsapp" value={editFormData.whatsapp} onChange={handleInputChange} className="form-input" />
            </div>
            <div className="mb-4">
              <label className="form-label">Saldo</label>
              <input type="number" name="saldo" value={editFormData.saldo} onChange={handleInputChange} className="form-input" />
            </div>
            <div className="flex justify-end gap-3">
                <button type="button" onClick={() => setIsEditModalOpen(false)} className="px-4 py-2 rounded bg-gray-2 hover:bg-gray-3 dark:bg-dark-2 dark:hover:bg-dark-3">Batal</button>
                <button type="submit" className="form-button-primary">Simpan Perubahan</button>
            </div>
          </form>
        </Modal>
      )}

    </div>
  );
};

export default UserTable;
