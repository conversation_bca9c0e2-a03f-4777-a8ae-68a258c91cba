"use client";

import { useState, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { HistoryApiResponse } from '@/types/history';
import dayjs from 'dayjs';
import { cn } from '@/lib/utils';
import { Eye } from 'lucide-react';
import { debounce } from 'lodash';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

const ITEMS_PER_PAGE = 10;

interface VlessHistoryTableProps {
  initialData: HistoryApiResponse;
}

const VlessHistoryTable = ({ initialData }: VlessHistoryTableProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Filter hanya data vless
  const vlessHistory = (initialData.history || []).filter(
    item => item.service_type.toLowerCase() === 'vless'
  );
  const totalData = vlessHistory.length;
  const currentPage = initialData.page || 1;
  const totalPages = Math.ceil(totalData / (initialData.limit || ITEMS_PER_PAGE));

  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || "");

  const debouncedNavigate = useCallback(debounce((query: string, page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', String(page));
    if (query) {
      params.set('search', query);
    } else {
      params.delete('search');
    }
    router.replace(`/product/vless/history?${params.toString()}`);
  }, 500), [router, searchParams]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = event.target.value;
    setSearchQuery(newQuery);
    debouncedNavigate(newQuery, 1); // Reset to page 1 for new search
  };

  const handleViewInvoice = (username: string, serverCode: string) => {
    router.push(`/detail/${username}/${serverCode}`);
  };

  const goToPage = (page: number) => {
    debouncedNavigate(searchQuery, page);
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  };

  const getPaginationLinks = () => {
    const links = [];
    links.push(
      <li key="prev">
        <button
          className="flex size-8 items-center justify-center rounded-[3px] hover:bg-primary hover:text-white disabled:pointer-events-none disabled:opacity-50"
          onClick={goToPreviousPage}
          disabled={currentPage === 1}
        >
          <span className="sr-only">Go to previous page</span>
          <svg width="18" height="18" viewBox="0 0 18 18" fill="currentColor">
            <path d="M12.1758 16.1158C12.007 16.1158 11.8383 16.0596 11.7258 15.9189L5.36953 9.45019C5.11641 9.19707 5.11641 8.80332 5.36953 8.5502L11.7258 2.08145C11.9789 1.82832 12.3727 1.82832 12.6258 2.08145C12.8789 2.33457 12.8789 2.72832 12.6258 2.98145L6.71953 9.0002L12.6539 15.0189C12.907 15.2721 12.907 15.6658 12.6539 15.9189C12.4852 16.0314 12.3445 16.1158 12.1758 16.1158Z"></path>
          </svg>
        </button>
      </li>
    );

    for (let i = 1; i <= totalPages; i++) {
        if (totalPages <= 5 || (i >= currentPage - 2 && i <= currentPage + 2) || i === 1 || i === totalPages) {
             links.push(
                <li key={i}>
                    <button
                    className={cn("flex items-center justify-center rounded-[3px] px-3 py-1.5 font-medium hover:bg-primary hover:text-white", { 'bg-primary text-white': currentPage === i })}
                    onClick={() => goToPage(i)}
                    >
                    {i}
                    </button>
                </li>
            );
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            links.push(<li key={`ellipsis-${i}`}><span className="flex items-center justify-center px-3 py-1.5">...</span></li>);
        }
    }

    links.push(
      <li key="next">
        <button
          className="flex size-8 items-center justify-center rounded-[3px] hover:bg-primary hover:text-white disabled:pointer-events-none disabled:opacity-50"
          onClick={goToNextPage}
          disabled={currentPage === totalPages}
        >
          <span className="sr-only">Go to next page</span>
          <svg width="18" height="18" viewBox="0 0 18 18" fill="currentColor">
            <path d="M5.81953 16.1158C5.65078 16.1158 5.51016 16.0596 5.36953 15.9471C5.11641 15.6939 5.11641 15.3002 5.36953 15.0471L11.2758 9.0002L5.36953 2.98145C5.11641 2.72832 5.11641 2.33457 5.36953 2.08145C5.62266 1.82832 6.01641 1.82832 6.26953 2.08145L12.6258 8.5502C12.8789 8.80332 12.8789 9.19707 12.6258 9.45019L6.26953 15.9189C6.15703 16.0314 5.98828 16.1158 5.81953 16.1158Z"></path>
          </svg>
        </button>
      </li>
    );

    return links;
  };

  // Filter berdasarkan search query
  const filteredHistory = vlessHistory.filter(item =>
    item.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.layanan.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.tipe.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Pagination untuk filtered data
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const paginatedHistory = filteredHistory.slice(startIndex, endIndex);

  return (
    <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-7.5">
      <div className="flex items-center justify-between mb-6">
        <h4 className="text-xl font-semibold text-black dark:text-white">
          History Layanan Vless
        </h4>
        <div className="relative">
          <input
            type="text"
            placeholder="Cari riwayat..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="flex items-center w-full max-w-xs gap-3.5 rounded-full border bg-gray-2 p-3 pl-10 outline-none ring-primary transition-colors focus-visible:ring-1 dark:border-dark-3 dark:bg-dark-2 dark:hover:border-dark-4 dark:hover:bg-dark-3 dark:hover:text-dark-6"
          />
          <div className="absolute top-1/2 left-3 -translate-y-1/2">
            <svg width="18" height="18" viewBox="0 0 18 18" fill="currentColor" className="max-[1015px]:size-5">
              <g clipPath="url(#clip0_1699_11536)">
                <path fillRule="evenodd" clipRule="evenodd" d="M8.625 2.0625C5.00063 2.0625 2.0625 5.00063 2.0625 8.625C2.0625 12.2494 5.00063 15.1875 8.625 15.1875C12.2494 15.1875 15.1875 12.2494 15.1875 8.625C15.1875 5.00063 12.2494 2.0625 8.625 2.0625ZM0.9375 8.625C0.9375 4.37931 4.37931 0.9375 8.625 0.9375C12.8707 0.9375 16.3125 4.37931 16.3125 8.625C16.3125 10.5454 15.6083 12.3013 14.4441 13.6487L16.8977 16.1023C17.1174 16.3219 17.1174 16.6781 16.8977 16.8977C16.6781 17.1174 16.3219 17.1174 16.1023 16.8977L13.6487 14.4441C12.3013 15.6083 10.5454 16.3125 8.625 16.3125C4.37931 16.3125 0.9375 12.8707 0.9375 8.625Z"></path>
              </g>
              <defs>
                <clipPath id="clip0_1699_11536">
                  <rect width="18" height="18" fill="white"></rect>
                </clipPath>
              </defs>
            </svg>
          </div>
        </div>
      </div>

      <div className="max-w-full overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="border-none bg-[#F7F9FC] dark:bg-dark-2 [&>th]:py-4 [&>th]:text-base [&>th]:text-dark [&>th]:dark:text-white">
              <TableHead className="min-w-[200px] xl:pl-7.5">ISP</TableHead>
              <TableHead>Username</TableHead>
              <TableHead>Tanggal</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Tipe</TableHead>
              <TableHead className="text-right xl:pr-7.5">Aksi</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedHistory.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-10">
                  {searchQuery ? 'Tidak ada data yang sesuai dengan pencarian' : 'Belum ada history layanan Trojan'}
                </TableCell>
              </TableRow>
            ) : (
              paginatedHistory.map((item, index) => (
                <TableRow key={index} className="border-[#eee] dark:border-dark-3">
                  <TableCell className="max-w-[22ch] truncate xl:pl-7.5">
                    <p className="font-medium text-black dark:text-white">
                      {item.nama_isp || item.nama_server}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {item.nama_server}
                    </p>
                  </TableCell>

                  <TableCell>
                    <p className="text-black dark:text-white">
                      {item.username}
                    </p>
                  </TableCell>

                  <TableCell>
                    <p className="text-black dark:text-white">{dayjs(item.tanggal_beli).format('DD MMM YYYY')}</p>
                    <p className="text-xs">Exp: {dayjs(item.expired).format('DD MMM YYYY')}</p>
                  </TableCell>

                  <TableCell>
                    {(() => {
                      const statusText = item.status.toUpperCase();
                      const statusType = item.status.toLowerCase();

                      if (statusType === 'active') {
                        return (
                          <span className="badge-pill-outline badge-pill-outline-success">
                            {statusText}
                          </span>
                        );
                      } else if (statusType === 'expired') {
                        return (
                          <span className="badge-pill-outline badge-pill-outline-danger">
                            {statusText}
                          </span>
                        );
                      } else {
                        return (
                          <span className="badge-pill-outline badge-pill-outline-warning">
                            {statusText}
                          </span>
                        );
                      }
                    })()}
                  </TableCell>

                  <TableCell>
                    {(() => {
                      const tipeText = item.tipe.replace('vless-', '').toUpperCase();
                      const tipeType = item.tipe.toLowerCase().trim();

                      if (tipeType.indexOf('monthly') !== -1) {
                        return (
                          <span className="badge-pill badge-pill-primary">
                            {tipeText}
                          </span>
                        );
                      } else if (tipeType.indexOf('trial') !== -1) {
                        return (
                          <span className="badge-pill badge-pill-info">
                            {tipeText}
                          </span>
                        );
                      } else if (tipeType.indexOf('hourly') !== -1) {
                        return (
                          <span className="badge-pill badge-pill-warning">
                            {tipeText}
                          </span>
                        );
                      } else {
                        return (
                          <span className="badge-pill badge-pill-danger">
                            {tipeText}
                          </span>
                        );
                      }
                    })()}
                  </TableCell>

                  <TableCell className="xl:pr-7.5">
                    <div className="flex items-center justify-end gap-x-3.5">
                      <button
                        onClick={() => handleViewInvoice(item.username, item.kode_server)}
                        className="hover:text-primary"
                        title="Lihat Detail Akun"
                      >
                        <span className="sr-only">Lihat Detail Akun</span>
                        <Eye size={20}/>
                      </button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="p-4 sm:p-6 xl:p-7.5">
        <nav>
          <ul className="flex flex-wrap items-center gap-1">
            {getPaginationLinks()}
          </ul>
        </nav>
      </div>
    </div>
  );
};

export default VlessHistoryTable;
