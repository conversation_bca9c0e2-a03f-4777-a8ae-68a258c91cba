import { IconProps } from "@/types/icon-props";

export function AlertWarningIcon(props: IconProps) {
  return (
    <svg width={36} height={36} viewBox="0 0 36 36" fill="none" {...props}>
      <rect width={36} height={36} rx={8} fill="#FBBF24" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.5 16.68c0-2.664 0-3.997.315-4.445.314-.448 1.567-.877 4.072-1.734l.478-.164c1.306-.447 1.959-.67 2.635-.67.676 0 1.33.223 2.635.67l.478.164c2.505.857 3.758 1.286 4.072 1.734.315.448.315 1.78.315 4.445v1.313c0 4.698-3.532 6.978-5.749 7.946-.601.263-.902.394-1.751.394-.85 0-1.15-.131-1.751-.394-2.216-.968-5.749-3.248-5.749-7.946V16.68zm7.5-2.639c.345 0 .625.28.625.625V18a.625.625 0 11-1.25 0v-3.334c0-.345.28-.625.625-.625zm0 7.292a.833.833 0 100-1.666.833.833 0 000 1.666z"
        fill="#fff"
      />
    </svg>
  );
}

export function AlertSuccessIcon(props: IconProps) {
  return (
    <svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <rect width={32} height={32} rx={6} fill="#22AD5C" />
      <path
        d="M23.298 10.827l-.011-.015-.013-.014a.87.87 0 00-1.308-.004l-8.047 8.66-3.862-4.167a.87.87 0 00-1.309.003.976.976 0 000 1.3h0l.005.005 4.114 4.44c.277.305.662.465 1.029.465.396 0 .756-.165 1.028-.465l8.292-8.923a.98.98 0 00.082-1.285z"
        fill="#fff"
        stroke="#fff"
      />
    </svg>
  );
}

export function AlertErrorIcon(props: IconProps) {
  return (
    <svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <rect width={32} height={32} rx={6} fill="#F56060" />
      <path
        d="M12.796 11.723h0l3.698 3.707 3.71-3.692.354.353-.353-.354h0a.756.756 0 011.072 0 .755.755 0 010 1.073h-.001l-3.71 3.693 3.696 3.705s0 0 0 0a.755.755 0 010 1.073l-.353-.354.353.354a.768.768 0 01-.529.219c-.19 0-.39-.08-.529-.22l.354-.353-.354.354-3.71-3.707-3.698 3.706h0a.77.77 0 01-.53.22c-.188 0-.39-.08-.528-.22l.353-.353-.353.354a.755.755 0 010-1.073l3.684-3.705-3.699-3.707s0 0 0 0a.755.755 0 010-1.073.756.756 0 011.073 0z"
        fill="#fff"
        stroke="#fff"
      />
    </svg>
  );
}
