"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';

export type NotificationType = 'success' | 'error' | 'info' | 'warning';

interface Notification {
  id: number;
  message: string;
  type: NotificationType;
}

interface NotificationContextType {
  addNotification: (message: string, type: NotificationType) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const addNotification = (message: string, type: NotificationType) => {
    const id = new Date().getTime();
    setNotifications(prev => [...prev, { id, message, type }]);

    // Automatically remove the notification after 5 seconds
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== id));
    }, 5000);
  };

  const removeNotification = (id: number) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  return (
    <NotificationContext.Provider value={{ addNotification }}>
      {children}
      <div className="fixed top-5 right-5 z-50 flex flex-col gap-3">
        {notifications.map(notification => (
          <div key={notification.id} className={`notification notification-${notification.type}`}>
            <div className="notification-content">
              <div className="notification-icon">
                {/* Icons can be added here based on type */}
              </div>
              <div>
                <p className="notification-title">{notification.type.charAt(0).toUpperCase() + notification.type.slice(1)}</p>
                <p className="notification-message">{notification.message}</p>
              </div>
            </div>
            <button onClick={() => removeNotification(notification.id)} className="notification-close">
              <span>&times;</span>
            </button>
          </div>
        ))}
      </div>
    </NotificationContext.Provider>
  );
};
