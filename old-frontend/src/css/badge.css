@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Badge Style 1: Solid */
  .badge-solid {
    @apply inline-flex rounded px-2 py-1 text-sm font-medium hover:bg-opacity-90;
  }
  .badge-solid-primary { @apply bg-primary text-white; }
  .badge-solid-secondary { @apply bg-[#13C296] text-white; }
  .badge-solid-dark { @apply bg-dark text-white; }
  .badge-solid-gray { @apply bg-dark-5 text-white; }
  .badge-solid-light { @apply bg-[#EFEFEF] text-dark; }
  .badge-solid-warning { @apply bg-[#F9C107] text-dark; }
  .badge-solid-danger { @apply bg-[#DC3545] text-white; }
  .badge-solid-success { @apply bg-[#3CA745] text-white; }
  .badge-solid-info { @apply bg-[#3BA2B8] text-white; }

  /* Badge Style 2: Outline */
  .badge-outline {
    @apply inline-flex rounded border px-2 py-[3px] text-body-sm font-medium hover:opacity-80;
  }
  .badge-outline-primary { @apply border-primary text-primary; }
  .badge-outline-secondary { @apply border-[#13C296] text-[#13C296]; }
  .badge-outline-dark { @apply border-dark text-dark dark:border-white dark:text-white; }
  .badge-outline-gray { @apply border-dark-5 text-dark-5; }
  .badge-outline-light { @apply border-[#EFEFEF] text-dark dark:text-white; }
  .badge-outline-warning { @apply border-[#F9C107] text-[#F9C107]; }
  .badge-outline-danger { @apply border-[#DC3545] text-[#DC3545]; }
  .badge-outline-success { @apply border-[#3CA745] text-[#3CA745]; }
  .badge-outline-info { @apply border-[#3BA2B8] text-[#3BA2B8]; }

  /* Badge Style 3: Pill Solid */
  .badge-pill {
    @apply inline-flex rounded-full px-3 py-[3px] text-body-sm font-medium hover:bg-opacity-90;
  }
  .badge-pill-primary { @apply bg-primary text-white !important; }
  .badge-pill-secondary { @apply bg-[#13C296] text-white !important; }
  .badge-pill-dark { @apply bg-dark text-white !important; }
  .badge-pill-gray { @apply bg-dark-5 text-white !important; }
  .badge-pill-light { @apply bg-[#EFEFEF] text-dark !important; }
  .badge-pill-warning { @apply bg-[#F9C107] text-dark !important; }
  .badge-pill-danger { @apply bg-[#DC3545] text-white !important; }
  .badge-pill-success { @apply bg-[#3CA745] text-white !important; }
  .badge-pill-info { @apply bg-[#3BA2B8] text-white !important; }

  /* Badge Style 4: Pill Outline */
  .badge-pill-outline {
    @apply inline-flex rounded-full border px-3 py-0.5 text-body-sm font-medium hover:opacity-80;
  }
  .badge-pill-outline-primary { @apply border-primary text-primary !important; }
  .badge-pill-outline-secondary { @apply border-[#13C296] text-[#13C296] !important; }
  .badge-pill-outline-dark { @apply border-dark text-dark dark:border-white dark:text-white !important; }
  .badge-pill-outline-gray { @apply border-dark-5 text-dark-5 !important; }
  .badge-pill-outline-light { @apply border-[#EFEFEF] text-dark dark:text-white !important; }
  .badge-pill-outline-warning { @apply border-[#F9C107] text-[#F9C107] !important; }
  .badge-pill-outline-danger { @apply border-[#DC3545] text-[#DC3545] !important; }
  .badge-pill-outline-success { @apply border-[#3CA745] text-[#3CA745] !important; }
  .badge-pill-outline-info { @apply border-[#3BA2B8] text-[#3BA2B8] !important; }
}
