/* ======== card style ======== */
.card {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}

.dark .card {
  background-color: #1A222C; /* .dark:bg-gray-dark */
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.3); /* .dark:shadow-card */
}

.card a {
  display: block;
}

.card-image {
  width: 100%;
  object-fit: cover;
  padding: 1rem 1rem 0 1rem; /* px-4 pt-4 */
  border-radius: 5px;
}

.card-header {
  padding: 1.25rem 1.875rem; /* px-7.5 py-5 */
  border-bottom: 1px solid #E2E8F0; /* border-stroke */
}

.dark .card-header {
    border-bottom-color: #2E3A47; /* .dark:border-dark-3 */
}

.card-title {
  font-size: 1.25rem; /* text-xl */
  font-weight: 600; /* font-semibold */
  color: #1C2434; /* text-dark */
  transition: color 0.2s ease-in-out;
}

.dark .card-title {
    color: #ffffff; /* .dark:text-white */
}

.card-title:hover {
    color: #3C50E0; /* hover:text-primary */
}

/* No need for dark hover rule if it's the same */

.card-content {
  padding: 1.5rem 1.875rem 2.25rem; /* px-7.5 pb-9 pt-6 */
  font-weight: 500; /* font-medium */
}

.dark .card-content {
    color: #A9B2C0; /* .dark:text-dark-6 */
}

/* Styles for the second card variant (with image and p-6 body) */
.card-body {
    padding: 1.5rem; /* p-6 */
}

.card-body .card-title {
    margin-bottom: 0.75rem; /* mb-3 */
}

.card-body .card-content {
    padding: 0;
}
