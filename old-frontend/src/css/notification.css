@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .notification {
    @apply rounded-lg flex items-center justify-between w-full max-w-md p-4 shadow-lg bg-white dark:bg-dark-2 border border-stroke dark:border-dark-3;
  }

  .notification-content {
    @apply flex items-center gap-4;
  }

  .notification-icon {
    @apply flex-shrink-0 flex items-center justify-center size-10 rounded-full text-white;
  }

  .notification-title {
    @apply font-semibold text-dark dark:text-white;
  }

  .notification-message {
    @apply text-sm text-gray-6 dark:text-dark-6;
  }

  .notification-close {
    @apply text-dark-6 hover:text-dark dark:hover:text-white shrink-0;
  }

  /* Variants */
  .notification-success .notification-icon {
    @apply bg-green;
  }

  .notification-error {
    @apply border-red-light-4 dark:border-red;
  }
  .notification-error .notification-icon {
    @apply bg-red;
  }
  .notification-error .notification-title {
    @apply text-red;
  }

  .notification-info .notification-icon {
    @apply bg-primary;
  }

  .notification-warning .notification-icon {
    @apply bg-yellow-dark;
  }
  .notification-warning .notification-title {
    @apply text-yellow-dark;
  }
}
