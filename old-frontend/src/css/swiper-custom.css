/* Custom Swiper Styles */

/* Container styling */
.server-slideshow-container {
  position: relative;
  width: 100%;
  overflow: visible; /* Changed from hidden to visible */
}

/* Ensure swiper container doesn't overflow */
.server-slideshow {
  overflow: hidden;
}

/* External navigation buttons styling */
.external-nav-prev,
.external-nav-next {
  cursor: pointer;
  user-select: none;
  outline: none;
  border: none;
  backdrop-filter: blur(8px);
}

.external-nav-prev:disabled,
.external-nav-next:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Hover effects for external navigation */
.external-nav-prev:hover,
.external-nav-next:hover {
  transform: scale(1.05);
}

/* Hide default swiper navigation buttons since we use external ones */
.server-slideshow .swiper-button-next,
.server-slideshow .swiper-button-prev {
  display: none;
}

/* Pagination bullets */
.server-slideshow .swiper-pagination-bullet {
  background: rgba(255, 255, 255, 0.7);
  opacity: 0.7;
}

.server-slideshow .swiper-pagination-bullet-active {
  background: #ffffff;
  opacity: 1;
}

/* Remove dark mode adjustments for hidden buttons */

/* Slideshow container */
.server-slideshow {
  position: relative;
}

/* Pagination positioning */
.server-slideshow .swiper-pagination {
  position: absolute;
  bottom: 0 !important;
  z-index: 10;
}