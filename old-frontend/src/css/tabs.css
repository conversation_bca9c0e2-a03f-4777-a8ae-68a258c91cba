/* ======== tabs style ======== */
.tab-container {
  border-bottom: 1px solid #E2E8F0; /* border-stroke */
  display: flex;
  flex-wrap: wrap;
  gap: 2.5rem; /* gap-10 */
}

.dark .tab-container {
  border-bottom-color: #2E3A47; /* dark:border-dark-3 */
}

.tab-button {
  font-weight: 500; /* font-medium */
  padding-bottom: 7px;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease-in-out;
}

.tab-button:hover {
  color: #3C50E0; /* hover:text-primary */
  border-bottom-color: #3C50E0; /* hover:border-primary */
}

.tab-button[data-active='true'] {
  color: #3C50E0; /* data-[active=true]:text-primary */
  border-bottom-color: #3C50E0; /* data-[active=true]:border-primary */
}

.tab-content {
  padding-top: 1.875rem; /* pt-7.5 */
}
