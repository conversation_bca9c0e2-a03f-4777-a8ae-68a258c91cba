import { useState, useEffect } from 'react';
import { PaymentChannel, PaymentChannelsResponse } from '@/app/api/payment/channels/route';

interface UsePaymentChannelsReturn {
  channels: PaymentChannel[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export const usePaymentChannels = (): UsePaymentChannelsReturn => {
  const [channels, setChannels] = useState<PaymentChannel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchChannels = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch('/api/payment/channels');
      const data: PaymentChannelsResponse = await response.json();
      
      if (response.ok && data.success) {
        // Filter hanya channel yang aktif
        const activeChannels = data.data.filter(channel => channel.active);
        setChannels(activeChannels);
      } else {
        setError(data.message || 'Gagal mengambil data payment channels');
      }
    } catch (err) {
      console.error('Error fetching payment channels:', err);
      setError('Terjadi kesalahan saat mengambil data payment channels');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchChannels();
  }, []);

  return {
    channels,
    isLoading,
    error,
    refetch: fetchChannels
  };
};

// Helper functions untuk grouping channels
export const groupChannelsByType = (channels: PaymentChannel[]) => {
  return channels.reduce((groups, channel) => {
    const group = channel.group;
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(channel);
    return groups;
  }, {} as Record<string, PaymentChannel[]>);
};

export const formatFee = (channel: PaymentChannel, amount: number) => {
  const flatFee = channel.fee_customer.flat;
  const percentFee = (amount * channel.fee_customer.percent) / 100;
  const totalFee = flatFee + percentFee;
  
  // Apply minimum and maximum fee if specified
  let finalFee = totalFee;
  if (channel.minimum_fee && finalFee < channel.minimum_fee) {
    finalFee = channel.minimum_fee;
  }
  if (channel.maximum_fee && finalFee > channel.maximum_fee) {
    finalFee = channel.maximum_fee;
  }
  
  return Math.round(finalFee);
};
