import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { apiClient } from '@/lib/apiClientEnhanced';

export interface NotificationData {
  account_id?: number;
  account_type?: string;
  username?: string;
  server_code?: string;
  amount?: number;
  invoice_id?: string;
  duration?: string;
  payment_method?: string;
}

export interface Notification {
  id: number;
  type: 'trial' | 'monthly' | 'hourly' | 'renewal' | 'topup' | 'payment' | 'billing';
  title: string;
  message: string;
  data?: NotificationData;
  is_read: boolean;
  created_at: string;
  read_at?: string;
}

export interface NotificationStats {
  unread_count: number;
  total_count: number;
}

export interface NotificationListResponse {
  notifications: Notification[];
  total: number;
  page: number;
  limit: number;
  unread_count: number;
}

export const useNotifications = () => {
  const { data: session } = useSession();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [stats, setStats] = useState<NotificationStats>({ unread_count: 0, total_count: 0 });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch notifications with pagination
  const fetchNotifications = useCallback(async (page = 1, limit = 10) => {
    if (!session?.accessToken) {
      setError('Tidak terautentikasi');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await apiClient.get<NotificationListResponse>(
        `/users/me/notifications?page=${page}&limit=${limit}`,
        { token: session.accessToken as string }
      );
      
      if (page === 1) {
        setNotifications(response.notifications);
      } else {
        setNotifications(prev => [...prev, ...response.notifications]);
      }

      setStats({
        unread_count: response.unread_count,
        total_count: response.total
      });

      return response;
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Gagal mengambil notifikasi';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [session?.accessToken]);

  // Fetch notification stats only
  const fetchStats = useCallback(async () => {
    if (!session?.accessToken) return null;

    try {
      const response = await apiClient.get<NotificationStats>(
        '/users/me/notifications/stats',
        { token: session.accessToken as string }
      );
      setStats(response);
      return response;
    } catch (err: any) {
      console.error('Failed to fetch notification stats:', err);
      return null;
    }
  }, [session?.accessToken]);

  // Mark specific notifications as read
  const markAsRead = useCallback(async (notificationIds: number[]) => {
    if (!session?.accessToken) {
      throw new Error('Tidak terautentikasi');
    }

    try {
      await apiClient.post('/users/me/notifications/mark-read', {
        notification_ids: notificationIds
      }, { token: session.accessToken as string });
      
      // Update local state
      setNotifications(prev => 
        prev.map(notif => 
          notificationIds.includes(notif.id) 
            ? { ...notif, is_read: true, read_at: new Date().toISOString() }
            : notif
        )
      );
      
      // Update stats
      setStats(prev => ({
        ...prev,
        unread_count: Math.max(0, prev.unread_count - notificationIds.length)
      }));
      
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Gagal menandai notifikasi sebagai dibaca';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [session?.accessToken]);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    if (!session?.accessToken) {
      throw new Error('Tidak terautentikasi');
    }

    try {
      await apiClient.post('/users/me/notifications/mark-all-read', {}, { token: session.accessToken as string });
      
      // Update local state
      setNotifications(prev => 
        prev.map(notif => ({ 
          ...notif, 
          is_read: true, 
          read_at: new Date().toISOString() 
        }))
      );
      
      // Update stats
      setStats(prev => ({ ...prev, unread_count: 0 }));
      
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Gagal menandai semua notifikasi sebagai dibaca';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [session?.accessToken]);

  // Delete notification
  const deleteNotification = useCallback(async (notificationId: number) => {
    if (!session?.accessToken) {
      throw new Error('Tidak terautentikasi');
    }

    try {
      await apiClient.delete(`/users/me/notifications/${notificationId}`, { token: session.accessToken as string });
      
      // Update local state
      const deletedNotification = notifications.find(n => n.id === notificationId);
      setNotifications(prev => prev.filter(notif => notif.id !== notificationId));
      
      // Update stats
      setStats(prev => ({
        total_count: Math.max(0, prev.total_count - 1),
        unread_count: deletedNotification && !deletedNotification.is_read 
          ? Math.max(0, prev.unread_count - 1)
          : prev.unread_count
      }));
      
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Gagal menghapus notifikasi';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [notifications, session?.accessToken]);

  // Auto-refresh stats periodically (polling approach)
  useEffect(() => {
    // Initial fetch
    fetchStats();
    
    // Set up polling every 30 seconds
    const interval = setInterval(fetchStats, 30000);
    
    return () => clearInterval(interval);
  }, [fetchStats]);

  return {
    notifications,
    stats,
    loading,
    error,
    fetchNotifications,
    fetchStats,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    // Helper functions
    hasUnread: stats.unread_count > 0,
    unreadCount: stats.unread_count,
    totalCount: stats.total_count
  };
};
