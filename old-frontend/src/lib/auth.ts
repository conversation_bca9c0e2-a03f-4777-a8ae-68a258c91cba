import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";

export const authOptions: NextAuthOptions = {
  providers: [
    // Provider untuk login email/password tradisional
    CredentialsProvider({
      id: "credentials",
      name: "Credentials",
      credentials: {
        identifier: { label: "Identifier", type: "text" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials) {
          return null;
        }

        try {
          // Panggil endpoint login di backend Anda
          const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
              identifier: credentials.identifier, 
              password: credentials.password 
            }),
          });

          const data = await res.json();

          // Jika login gagal, backend sekarang mengembalikan { user, accessToken }
          if (!res.ok || !data.user || !data.accessToken) {
            const errorMessage = data.error || "Kredensial tidak valid atau terjadi kesalahan.";
            throw new Error(errorMessage);
          }

          // Kembalikan objek yang akan disimpan di session JWT
          // Strukturnya sekarang sama dengan provider telegram
          return {
            ...data.user,
            accessToken: data.accessToken,
          };
        } catch (error: any) {
          // Log error untuk debugging di sisi server
          console.error("Authorize Error:", error);
          // Lempar error agar bisa ditangkap oleh NextAuth dan ditampilkan ke pengguna
          throw new Error(error.message || "Terjadi kesalahan saat mencoba masuk.");
        }
      },
    }),
    // Provider untuk login via Telegram
    CredentialsProvider({
      id: "telegram",
      name: "Telegram",
      credentials: {
        user_data: { label: "User Data", type: "text" },
      },
      async authorize(credentials) {
        if (!credentials?.user_data) {
          return null;
        }

        try {
          const telegramUserData = JSON.parse(credentials.user_data);

          const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/telegram`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(telegramUserData),
          });

          const responseData = await res.json();

          if (res.ok && responseData && responseData.user && responseData.accessToken) {
            // Backend mengembalikan { user: {...}, accessToken: "..." }
            // Kita harus mengembalikan objek yang sama agar NextAuth dapat memprosesnya di callback jwt
            return responseData;
          }
          
          // Jika backend mengembalikan error atau formatnya salah, log error tersebut
          console.error("Telegram auth failed:", responseData.error || "Unknown error from backend");
          return null;
        } catch (error: any) {
          console.error("Error in Telegram authorize function:", error);
          // Melempar error agar bisa ditangkap oleh frontend jika diperlukan
          throw new Error(error.message || "An unexpected error occurred during Telegram login.");
        }
      },
    }),
  ],

  callbacks: {
    async jwt({ token, user }) {
      // Saat login pertama kali, objek 'user' dari provider tersedia.
      // Kita salin accessToken dan data user ke dalam token JWT.
      if (user) {
        return {
          ...token,
          accessToken: user.accessToken,
          user: user, // The user object from the authorize callback
        };
      }
      return token;
    },

    async session({ session, token }) {
      // Setiap kali sesi diakses, data dari token disalin ke objek sesi.
      // Ini membuat data tersedia di sisi client melalui useSession() atau getServerSession().
      session.user = token.user;
      session.accessToken = token.accessToken;
      return session;
    },
  },
  pages: {
    signIn: '/', // Our login page is the root page
  },
  session: {
    strategy: "jwt",
    // Session expires in 30 minutes, matching the backend token lifetime.
    maxAge: parseInt(process.env.NEXTAUTH_SESSION_MAX_AGE || '1800', 10),
  },
  secret: process.env.NEXTAUTH_SECRET,
};