import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Function to parse JWT payload without a full library
function parseJwtPayload(token: string) {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(function (c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        })
        .join(''),
    );
    return JSON.parse(jsonPayload);
  } catch (e) {
    return null;
  }
}

export async function middleware(req: NextRequest) {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  const { pathname } = req.nextUrl;

  let isEffectivelyLoggedIn = false;

  if (token && token.accessToken) {
    const backendTokenPayload = parseJwtPayload(token.accessToken as string);
    const currentTime = Math.floor(Date.now() / 1000);

    // Check if the backend token is not expired
    if (backendTokenPayload && backendTokenPayload.exp > currentTime) {
      isEffectivelyLoggedIn = true;
    }
  }

  // 1. If user is logged in and on the login page, redirect to dashboard
  if (isEffectivelyLoggedIn && pathname === '/') {
    return NextResponse.redirect(new URL('/dashboard', req.url));
  }

  // 2. Define protected routes
  const protectedRoutes = ['/dashboard', '/admin', '/profile', '/forms', '/tables', '/pages', '/product', '/history'];
  const isProtectedRoute = protectedRoutes.some(path => pathname.startsWith(path));

  // 3. If user is NOT logged in and tries to access a protected route, redirect to login
  if (!isEffectivelyLoggedIn && isProtectedRoute) {
    // To prevent redirect loops for the login page itself
    if (pathname !== '/') {
      return NextResponse.redirect(new URL('/', req.url));
    }
  }

  // 4. Allow API routes to handle their own authentication
  if (pathname.startsWith('/api/')) {
    return NextResponse.next();
  }

  return NextResponse.next();
}

// This config uses a regex to match all paths except for static files and API routes.
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};

