export interface ServiceHistoryItem {
  tanggal_beli: string;
  username: string;
  layanan: string;
  status: string;
  tipe: string;
  service_type: string;
  order_id: string;
  kode_akun: string;
  domain: string;
  harga: string;
  durasi: string;
  expired: string;
  uuid: string;
  kode_server: string;
  nama_server?: string;
  nama_isp?: string;
  password?: string;
}

export interface HistoryApiResponse {
  history: ServiceHistoryItem[];
  total: number;
  page: number;
  limit: number;
  message?: string;
}
