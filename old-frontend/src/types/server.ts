export interface Server {
    id?: number;
    server_id: number;
    nama: string;
    kode: string;
    domain: string;
    token: string;
    negara: string;
    nama_isp: string;
    harga_member: number;
    harga_reseller: number;
    ssh: 'enabled' | 'disabled';
    trojan: 'enabled' | 'disabled' | 'enable';
    vmess: 'enabled' | 'disabled' | 'enable';
    vless: 'enabled' | 'disabled' | 'enable';
    slot_server: number;
    slot_terpakai: number;
    total_user: number;
    max_device: number;
    created_at: string;
    updated_at: string;
}
